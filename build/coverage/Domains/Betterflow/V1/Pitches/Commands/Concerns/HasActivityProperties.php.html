<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /Users/<USER>/Developer/clients/GPJ/lighthouse-api/app/Domains/Betterflow/V1/Pitches/Commands/Concerns/HasActivityProperties.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../../_css/bootstrap.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../../_css/octicons.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../../_css/style.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../../index.html">/Users/<USER>/Developer/clients/GPJ/lighthouse-api/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../../index.html">Domains</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Betterflow</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">V1</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Pitches</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Commands</a></li>
         <li class="breadcrumb-item"><a href="index.html">Concerns</a></li>
         <li class="breadcrumb-item active">HasActivityProperties.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">14&nbsp;/&nbsp;14</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties">HasActivityProperties</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">14&nbsp;/&nbsp;14</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success small">3</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#10"><abbr title="changes(Illuminate\Database\Eloquent\Model $model): mixed">changes</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">8&nbsp;/&nbsp;8</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#26"><abbr title="properties(Illuminate\Database\Eloquent\Model $model): array">properties</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#31"><abbr title="state(Illuminate\Database\Eloquent\Model $model): array">state</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">5&nbsp;/&nbsp;5</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Pitches\Commands\Concerns</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Database\Eloquent\Model</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">trait</span><span class="default">&nbsp;</span><span class="default">HasActivityProperties</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">changes</span><span class="keyword">(</span><span class="default">Model</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">mixed</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 12" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$attributes</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="default">-&gt;</span><span class="default">fresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">getAttributes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 14" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$lastStateModel</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="default">-&gt;</span><span class="default">fresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">activities</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">latest</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">forEvents</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">RecordableEvents</span><span class="default">::</span><span class="default">DraftCreated</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">RecordableEvents</span><span class="default">::</span><span class="default">PitchUpdated</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 16" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$lastState</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$lastStateModel</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'properties.old'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 18" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$diff</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">collect</span><span class="keyword">(</span><span class="default">$attributes</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">diffAssoc</span><span class="keyword">(</span><span class="default">$lastState</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 20" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 21" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'changes'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$diff</span><span class="default">-&gt;</span><span class="default">except</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'public_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'created_at'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'updated_at'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 22" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'old'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$attributes</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 23" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">properties</span><span class="keyword">(</span><span class="default">Model</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="14 tests cover line 28" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\CrudTest::authenticated_users_with_permissions_can_create_a_pitch_with_form&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">changes</span><span class="keyword">(</span><span class="default">$model</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">state</span><span class="keyword">(</span><span class="default">Model</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="12 tests cover line 34" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$model</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="default">-&gt;</span><span class="default">fresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="12 tests cover line 36" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="12 tests cover line 37" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'current'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="default">-&gt;</span><span class="default">currentState</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="12 tests cover line 38" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'previous'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="default">-&gt;</span><span class="default">previousState</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="12 tests cover line 39" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_submits_pitch_and_records_activity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_assigns_pitch_to_vertical_editor_if_exists&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandlerTest::handle_method_does_not_assign_pitch_if_no_vertical_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\PitchesTest::it_gets_pitches_grouped_with_drafts&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::reporter_can_submit_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editors_or_admins_can_decline_a_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_schedule_a_pitch_for_a_pitch_meeting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_return_a_pitch_for_revision&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::editor_can_send_a_pitch_for_travel_approval&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.8</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.16</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Thu Jan 23 11:19:27 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../../../../_js/jquery.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../../_js/bootstrap.bundle.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../../_js/file.js?v=11.0.8" type="text/javascript"></script>
 </body>
</html>
