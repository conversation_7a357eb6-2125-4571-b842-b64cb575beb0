<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /Users/<USER>/Developer/clients/GPJ/lighthouse-api/app/Domains/Betterflow/V1/Editorials/Casts/EditorialPhaseCast.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../_css/bootstrap.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/octicons.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/style.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/Developer/clients/GPJ/lighthouse-api/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Domains</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Betterflow</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">V1</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Editorials</a></li>
         <li class="breadcrumb-item"><a href="index.html">Casts</a></li>
         <li class="breadcrumb-item active">EditorialPhaseCast.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="visually-hidden">83.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">83.33%</div></td>
       <td class="warning small"><div align="right">5&nbsp;/&nbsp;6</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="visually-hidden">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;2</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><abbr title="App\Domains\Betterflow\V1\Editorials\Casts\EditorialPhaseCast">EditorialPhaseCast</abbr></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="visually-hidden">83.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">83.33%</div></td>
       <td class="warning small"><div align="right">5&nbsp;/&nbsp;6</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="visually-hidden">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;2</div></td>
       <td class="danger small">4.07</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#16"><abbr title="get(Illuminate\Database\Eloquent\Model $model, string $key, mixed $value, array $attributes): mixed">get</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="80.00" aria-valuemin="0" aria-valuemax="100" style="width: 80.00%">
           <span class="visually-hidden">80.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">80.00%</div></td>
       <td class="warning small"><div align="right">4&nbsp;/&nbsp;5</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">3.07</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#34"><abbr title="set(Illuminate\Database\Eloquent\Model $model, string $key, mixed $value, array $attributes): mixed">set</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Casts</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Contracts\Database\Eloquent\CastsAttributes</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Database\Eloquent\Model</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">EditorialPhaseCast</span><span class="default">&nbsp;</span><span class="keyword">implements</span><span class="default">&nbsp;</span><span class="default">CastsAttributes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Cast&nbsp;the&nbsp;given&nbsp;value.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string,&nbsp;mixed&gt;&nbsp;&nbsp;$attributes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">get</span><span class="keyword">(</span><span class="default">Model</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$key</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">mixed</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$attributes</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">mixed</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="25 tests cover line 18" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_list_assets_for_an_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_show_a_specific_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_update_an_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_not_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::if_not_completed_added_to_request_asset_is_not_updated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_user_without_permission&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::test_validation_fails_for_invalid_user_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_editing_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase_publish&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_can_request_an_assist_for_a_reporting_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_requires_valid_data_to_request_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_handles_optional_fields_when_requesting_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="25 tests cover line 22" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_list_assets_for_an_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_show_a_specific_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_update_an_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_not_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::if_not_completed_added_to_request_asset_is_not_updated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_user_without_permission&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::test_validation_fails_for_invalid_user_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_editing_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase_publish&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_can_request_an_assist_for_a_reporting_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_requires_valid_data_to_request_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_handles_optional_fields_when_requesting_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$value</span><span class="default">&nbsp;</span><span class="keyword">instanceof</span><span class="default">&nbsp;</span><span class="default">EditorialPhase</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 23" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="25 tests cover line 26" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_list_assets_for_an_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_show_a_specific_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_update_an_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_not_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::if_not_completed_added_to_request_asset_is_not_updated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_user_without_permission&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::test_validation_fails_for_invalid_user_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_editing_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase_publish&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_can_request_an_assist_for_a_reporting_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_requires_valid_data_to_request_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_handles_optional_fields_when_requesting_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">EditorialPhase</span><span class="default">::</span><span class="default">tryFrom</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Prepare&nbsp;the&nbsp;given&nbsp;value&nbsp;for&nbsp;storage.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string,&nbsp;mixed&gt;&nbsp;&nbsp;$attributes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">set</span><span class="keyword">(</span><span class="default">Model</span><span class="default">&nbsp;</span><span class="default">$model</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$key</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">mixed</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$attributes</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">mixed</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="25 tests cover line 36" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandlerTest::test_it_approves_the_pitch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_list_assets_for_an_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_show_a_specific_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\AssetsControllerTest::it_can_update_an_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_not_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::if_not_completed_added_to_request_asset_is_not_updated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_user_without_permission&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::test_validation_fails_for_invalid_user_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_editing_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\MoveEditorialPhaseTest::it_can_move_editorial_phase_publish&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_can_request_an_assist_for_a_reporting_asset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_requires_valid_data_to_request_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\RequestReportingAssetAssistTest::it_handles_optional_fields_when_requesting_an_assist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Pitches\StateActionsTest::vertical_editor_can_approve_a_pitch&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.8</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.16</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Thu Jan 23 11:19:27 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../../../_js/jquery.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../_js/bootstrap.bundle.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../_js/file.js?v=11.0.8" type="text/javascript"></script>
 </body>
</html>
