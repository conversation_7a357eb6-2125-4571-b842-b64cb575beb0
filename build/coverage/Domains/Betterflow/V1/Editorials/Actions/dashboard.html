<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Developer/clients/GPJ/lighthouse-api/app/Domains/Betterflow/V1/Editorials/Actions</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../_css/bootstrap.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/nv.d3.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/style.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/Developer/clients/GPJ/lighthouse-api/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Domains</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Betterflow</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">V1</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Editorials</a></li>
         <li class="breadcrumb-item"><a href="index.html">Actions</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Tasks/IncompleteTask.php.html#16">App\Domains\Betterflow\V1\Editorials\Actions\Tasks\IncompleteTask</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WatchEditorial.php.html#10">App\Domains\Betterflow\V1\Editorials\Actions\WatchEditorial</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateAsset.php.html#15">App\Domains\Betterflow\V1\Editorials\Actions\UpdateAsset</a></td><td class="text-right">57%</td></tr>
       <tr><td><a href="CreateAsset.php.html#27">App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset</a></td><td class="text-right">74%</td></tr>
       <tr><td><a href="Tasks/CompleteEditorialTask.php.html#12">App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteEditorialTask</a></td><td class="text-right">75%</td></tr>
       <tr><td><a href="Tasks/CompleteTask.php.html#15">App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask</a></td><td class="text-right">78%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Tasks/IncompleteTask.php.html#16">App\Domains\Betterflow\V1\Editorials\Actions\Tasks\IncompleteTask</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CreateAsset.php.html#27">App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdateAsset.php.html#15">App\Domains\Betterflow\V1\Editorials\Actions\UpdateAsset</a></td><td class="text-right">17</td></tr>
       <tr><td><a href="Tasks/CompleteTask.php.html#15">App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Tasks/CompleteEditorialTask.php.html#12">App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteEditorialTask</a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Tasks/IncompleteTask.php.html#20"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\IncompleteTask::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tasks/IncompleteTask.php.html#53"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\IncompleteTask::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WatchEditorial.php.html#14"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\WatchEditorial::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WatchEditorial.php.html#19"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\WatchEditorial::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WatchEditorial.php.html#26"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\WatchEditorial::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WatchEditorial.php.html#31"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\WatchEditorial::jsonResponse">jsonResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateAsset.php.html#25"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\UpdateAsset::handle">handle</abbr></a></td><td class="text-right">38%</td></tr>
       <tr><td><a href="CreateAsset.php.html#71"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">54%</td></tr>
       <tr><td><a href="CreateAsset.php.html#31"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::handle">handle</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="CreateAsset.php.html#131"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::jsonResponse">jsonResponse</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="CreateAsset.php.html#140"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::makeAssetFor">makeAssetFor</abbr></a></td><td class="text-right">71%</td></tr>
       <tr><td><a href="Tasks/CompleteTask.php.html#38"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask::asController">asController</abbr></a></td><td class="text-right">72%</td></tr>
       <tr><td><a href="Tasks/CompleteEditorialTask.php.html#16"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteEditorialTask::asController">asController</abbr></a></td><td class="text-right">75%</td></tr>
       <tr><td><a href="CreateAsset.php.html#90"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::asController">asController</abbr></a></td><td class="text-right">76%</td></tr>
       <tr><td><a href="Tasks/CompleteTask.php.html#19"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask::handle">handle</abbr></a></td><td class="text-right">87%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Tasks/IncompleteTask.php.html#20"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\IncompleteTask::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdateAsset.php.html#25"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\UpdateAsset::handle">handle</abbr></a></td><td class="text-right">14</td></tr>
       <tr><td><a href="Tasks/IncompleteTask.php.html#53"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\IncompleteTask::asController">asController</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CreateAsset.php.html#140"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::makeAssetFor">makeAssetFor</abbr></a></td><td class="text-right">9</td></tr>
       <tr><td><a href="CreateAsset.php.html#90"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::asController">asController</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Tasks/CompleteTask.php.html#19"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask::handle">handle</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="CreateAsset.php.html#71"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="CreateAsset.php.html#31"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::handle">handle</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="CreateAsset.php.html#131"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset::jsonResponse">jsonResponse</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Tasks/CompleteTask.php.html#38"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask::asController">asController</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Tasks/CompleteEditorialTask.php.html#16"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteEditorialTask::asController">asController</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.8</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.16</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Thu Jan 23 11:19:27 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../_js/jquery.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../_js/d3.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../_js/nv.d3.min.js?v=11.0.8" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([2,0,0,0,0,0,1,0,3,0,0,3], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,1,0,1,2,4,1,0,20], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[74.28571428571429,22,"<a href=\"CreateAsset.php.html#27\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset<\/a>"],[100,4,"<a href=\"MoveEditorialPhase.php.html#11\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\MoveEditorialPhase<\/a>"],[100,5,"<a href=\"RequestAssetAssist.php.html#15\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestAssetAssist<\/a>"],[100,5,"<a href=\"RequestEditorialAssist.php.html#14\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestEditorialAssist<\/a>"],[75,2,"<a href=\"Tasks\/CompleteEditorialTask.php.html#12\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\CompleteEditorialTask<\/a>"],[78.94736842105263,6,"<a href=\"Tasks\/CompleteTask.php.html#15\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\CompleteTask<\/a>"],[0,8,"<a href=\"Tasks\/IncompleteTask.php.html#16\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\IncompleteTask<\/a>"],[57.692307692307686,10,"<a href=\"UpdateAsset.php.html#15\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\UpdateAsset<\/a>"],[0,4,"<a href=\"WatchEditorial.php.html#10\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\WatchEditorial<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[60,3,"<a href=\"CreateAsset.php.html#31\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset::handle<\/a>"],[100,1,"<a href=\"CreateAsset.php.html#50\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset::authorize<\/a>"],[100,1,"<a href=\"CreateAsset.php.html#55\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset::rules<\/a>"],[54.54545454545454,3,"<a href=\"CreateAsset.php.html#71\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset::prepareForValidation<\/a>"],[76.92307692307693,4,"<a href=\"CreateAsset.php.html#90\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset::asController<\/a>"],[66.66666666666666,2,"<a href=\"CreateAsset.php.html#131\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset::jsonResponse<\/a>"],[71.42857142857143,8,"<a href=\"CreateAsset.php.html#140\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\CreateAsset::makeAssetFor<\/a>"],[100,1,"<a href=\"MoveEditorialPhase.php.html#15\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\MoveEditorialPhase::authorize<\/a>"],[100,1,"<a href=\"MoveEditorialPhase.php.html#20\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\MoveEditorialPhase::handle<\/a>"],[100,1,"<a href=\"MoveEditorialPhase.php.html#33\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\MoveEditorialPhase::asController<\/a>"],[100,1,"<a href=\"MoveEditorialPhase.php.html#38\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\MoveEditorialPhase::jsonResponse<\/a>"],[100,1,"<a href=\"RequestAssetAssist.php.html#19\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestAssetAssist::authorize<\/a>"],[100,1,"<a href=\"RequestAssetAssist.php.html#25\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestAssetAssist::rules<\/a>"],[100,1,"<a href=\"RequestAssetAssist.php.html#37\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestAssetAssist::handle<\/a>"],[100,1,"<a href=\"RequestAssetAssist.php.html#42\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestAssetAssist::asController<\/a>"],[100,1,"<a href=\"RequestAssetAssist.php.html#52\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestAssetAssist::taskFromRequest<\/a>"],[100,1,"<a href=\"RequestEditorialAssist.php.html#18\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestEditorialAssist::authorize<\/a>"],[100,1,"<a href=\"RequestEditorialAssist.php.html#23\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestEditorialAssist::rules<\/a>"],[100,1,"<a href=\"RequestEditorialAssist.php.html#35\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestEditorialAssist::handle<\/a>"],[100,1,"<a href=\"RequestEditorialAssist.php.html#40\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestEditorialAssist::asController<\/a>"],[100,1,"<a href=\"RequestEditorialAssist.php.html#50\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\RequestEditorialAssist::taskFromRequest<\/a>"],[75,2,"<a href=\"Tasks\/CompleteEditorialTask.php.html#16\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\CompleteEditorialTask::asController<\/a>"],[87.5,4,"<a href=\"Tasks\/CompleteTask.php.html#19\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\CompleteTask::handle<\/a>"],[72.72727272727273,2,"<a href=\"Tasks\/CompleteTask.php.html#38\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\CompleteTask::asController<\/a>"],[0,5,"<a href=\"Tasks\/IncompleteTask.php.html#20\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\IncompleteTask::handle<\/a>"],[0,3,"<a href=\"Tasks\/IncompleteTask.php.html#53\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\Tasks\\IncompleteTask::asController<\/a>"],[100,1,"<a href=\"UpdateAsset.php.html#20\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\UpdateAsset::authorize<\/a>"],[38.88888888888889,6,"<a href=\"UpdateAsset.php.html#25\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\UpdateAsset::handle<\/a>"],[100,1,"<a href=\"UpdateAsset.php.html#64\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\UpdateAsset::rules<\/a>"],[100,1,"<a href=\"UpdateAsset.php.html#73\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\UpdateAsset::asController<\/a>"],[100,1,"<a href=\"UpdateAsset.php.html#78\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\UpdateAsset::jsonResponse<\/a>"],[0,1,"<a href=\"WatchEditorial.php.html#14\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\WatchEditorial::authorize<\/a>"],[0,1,"<a href=\"WatchEditorial.php.html#19\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\WatchEditorial::handle<\/a>"],[0,1,"<a href=\"WatchEditorial.php.html#26\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\WatchEditorial::asController<\/a>"],[0,1,"<a href=\"WatchEditorial.php.html#31\">App\\Domains\\Betterflow\\V1\\Editorials\\Actions\\WatchEditorial::jsonResponse<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
