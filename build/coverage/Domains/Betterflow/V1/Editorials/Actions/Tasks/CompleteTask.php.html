<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /Users/<USER>/Developer/clients/GPJ/lighthouse-api/app/Domains/Betterflow/V1/Editorials/Actions/Tasks/CompleteTask.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../../_css/bootstrap.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../../_css/octicons.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../../_css/style.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../../index.html">/Users/<USER>/Developer/clients/GPJ/lighthouse-api/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../../index.html">Domains</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Betterflow</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">V1</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Editorials</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Tasks</a></li>
         <li class="breadcrumb-item active">CompleteTask.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="78.95" aria-valuemin="0" aria-valuemax="100" style="width: 78.95%">
           <span class="visually-hidden">78.95% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">78.95%</div></td>
       <td class="warning small"><div align="right">15&nbsp;/&nbsp;19</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask">CompleteTask</abbr></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="78.95" aria-valuemin="0" aria-valuemax="100" style="width: 78.95%">
           <span class="visually-hidden">78.95% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">78.95%</div></td>
       <td class="warning small"><div align="right">15&nbsp;/&nbsp;19</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger small">6.34</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#19"><abbr title="handle(App\Domains\Betterflow\V1\Shared\Tasks\Models\Task $task, mixed $notes): void">handle</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="87.50" aria-valuemin="0" aria-valuemax="100" style="width: 87.50%">
           <span class="visually-hidden">87.50% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">87.50%</div></td>
       <td class="warning small"><div align="right">7&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4.03</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#38"><abbr title="asController(Lorisleiva\Actions\ActionRequest $request, App\Domains\Betterflow\V1\Editorials\Models\Editorial $editorial, ?App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset $asset, App\Domains\Betterflow\V1\Shared\Tasks\Models\Task $task)">asController</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="72.73" aria-valuemin="0" aria-valuemax="100" style="width: 72.73%">
           <span class="visually-hidden">72.73% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">72.73%</div></td>
       <td class="warning small"><div align="right">8&nbsp;/&nbsp;11</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2.08</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Actions\Tasks</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Events\TaskCompleted</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Editorial</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Shared\Tasks\Models\Task</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">InvalidArgumentException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\ActionRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\Concerns\AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Throwable</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">CompleteTask</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">Task</span><span class="default">&nbsp;</span><span class="default">$task</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">mixed</span><span class="default">&nbsp;</span><span class="default">$notes</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="4 tests cover line 21" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$task</span><span class="default">-&gt;</span><span class="default">completed</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">InvalidArgumentException</span><span class="keyword">(</span><span class="default">'task&nbsp;is&nbsp;already&nbsp;completed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="4 tests cover line 25" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$task</span><span class="default">-&gt;</span><span class="default">notes</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$notes</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="4 tests cover line 27" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$task</span><span class="default">-&gt;</span><span class="default">complete</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="4 tests cover line 29" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$task</span><span class="default">-&gt;</span><span class="default">taskable</span><span class="default">-&gt;</span><span class="default">hasPendingAssists</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$task</span><span class="default">-&gt;</span><span class="default">isAssist</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="2 tests cover line 30" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$task</span><span class="default">-&gt;</span><span class="default">taskable</span><span class="default">-&gt;</span><span class="default">ownerTask</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">?-&gt;</span><span class="default">removeHold</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="4 tests cover line 33" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$task</span><span class="default">-&gt;</span><span class="default">persist</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="4 tests cover line 35" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialProductionWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">TaskCompleted</span><span class="default">::</span><span class="default">dispatch</span><span class="keyword">(</span><span class="default">$task</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">asController</span><span class="keyword">(</span><span class="default">ActionRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Editorial</span><span class="default">&nbsp;</span><span class="default">$editorial</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">Asset</span><span class="default">&nbsp;</span><span class="default">$asset</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Task</span><span class="default">&nbsp;</span><span class="default">$task</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 40" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">rescue</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$request</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$task</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 41" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$notes</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 42" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">has</span><span class="keyword">(</span><span class="default">'notes'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 43" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$notes</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'notes'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 46" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">$task</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$notes</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 48" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">TaskResource</span><span class="default">::</span><span class="default">make</span><span class="keyword">(</span><span class="default">$task</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 49" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">response</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">json</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'message'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">500</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 53" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Assets\UpdateAssetTest::it_can_mark_an_asset_complete&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.8</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.16</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Thu Jan 23 11:19:27 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../../../../_js/jquery.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../../_js/bootstrap.bundle.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../../_js/file.js?v=11.0.8" type="text/javascript"></script>
 </body>
</html>
