<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /Users/<USER>/Developer/clients/GPJ/lighthouse-api/app/Domains/Betterflow/V1/Editorials/Actions/CreateAsset.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../_css/bootstrap.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/octicons.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/style.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/Developer/clients/GPJ/lighthouse-api/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Domains</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Betterflow</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">V1</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Editorials</a></li>
         <li class="breadcrumb-item"><a href="index.html">Actions</a></li>
         <li class="breadcrumb-item active">CreateAsset.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="74.29" aria-valuemin="0" aria-valuemax="100" style="width: 74.29%">
           <span class="visually-hidden">74.29% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">74.29%</div></td>
       <td class="warning small"><div align="right">52&nbsp;/&nbsp;70</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="28.57" aria-valuemin="0" aria-valuemax="100" style="width: 28.57%">
           <span class="visually-hidden">28.57% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">28.57%</div></td>
       <td class="danger small"><div align="right">2&nbsp;/&nbsp;7</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><abbr title="App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset">CreateAsset</abbr></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="74.29" aria-valuemin="0" aria-valuemax="100" style="width: 74.29%">
           <span class="visually-hidden">74.29% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">74.29%</div></td>
       <td class="warning small"><div align="right">52&nbsp;/&nbsp;70</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="28.57" aria-valuemin="0" aria-valuemax="100" style="width: 28.57%">
           <span class="visually-hidden">28.57% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">28.57%</div></td>
       <td class="danger small"><div align="right">2&nbsp;/&nbsp;7</div></td>
       <td class="danger small">30.23</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#31"><abbr title="handle(string $type, $userId, array $data): App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset">handle</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="60.00" aria-valuemin="0" aria-valuemax="100" style="width: 60.00%">
           <span class="visually-hidden">60.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">60.00%</div></td>
       <td class="warning small"><div align="right">6&nbsp;/&nbsp;10</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">3.58</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#50"><abbr title="authorize(Illuminate\Http\Request $request, App\Domains\Betterflow\V1\Editorials\Models\Editorial $editorial): bool">authorize</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#55"><abbr title="rules(): array">rules</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">12&nbsp;/&nbsp;12</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#71"><abbr title="prepareForValidation(Lorisleiva\Actions\ActionRequest $request): void">prepareForValidation</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="54.55" aria-valuemin="0" aria-valuemax="100" style="width: 54.55%">
           <span class="visually-hidden">54.55% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">54.55%</div></td>
       <td class="warning small"><div align="right">6&nbsp;/&nbsp;11</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">3.85</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#90"><abbr title="asController(Lorisleiva\Actions\ActionRequest $request, App\Domains\Betterflow\V1\Editorials\Models\Editorial $editorial): Illuminate\Support\Collection">asController</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="76.92" aria-valuemin="0" aria-valuemax="100" style="width: 76.92%">
           <span class="visually-hidden">76.92% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">76.92%</div></td>
       <td class="warning small"><div align="right">20&nbsp;/&nbsp;26</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4.20</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#131"><abbr title="jsonResponse(Illuminate\Support\Collection $assets): \Illuminate\Http\Resources\Json\AnonymousResourceCollection|\App\Domains\Betterflow\V1\Editorials\Http\Resources\AssetsResource">jsonResponse</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="66.67" aria-valuemin="0" aria-valuemax="100" style="width: 66.67%">
           <span class="visually-hidden">66.67% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">66.67%</div></td>
       <td class="warning small"><div align="right">2&nbsp;/&nbsp;3</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2.15</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#140"><abbr title="makeAssetFor(string $roleOrType): App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset">makeAssetFor</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="71.43" aria-valuemin="0" aria-valuemax="100" style="width: 71.43%">
           <span class="visually-hidden">71.43% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">71.43%</div></td>
       <td class="warning small"><div align="right">5&nbsp;/&nbsp;7</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="visually-hidden">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">9.49</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Actions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Enums\AssetType</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Events\AssetCreated</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Http\Resources\AssetsResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\Fact</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\Graphic</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\HeadlineSummary</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\Illustration</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\Photo</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Assets\Reporting</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Betterflow\V1\Editorials\Models\Editorial</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Domains\Shared\Helpers\LanguageHelper</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\AssetExistsException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\Request</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\Resources\Json\AnonymousResourceCollection</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Collection</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\DB</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Validation\Rule</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\ActionRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\Concerns\AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="comment">//&nbsp;TODO:&nbsp;See&nbsp;https://www.laravelactions.com/2.x/dispatch-jobs.html#unique-jobs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">CreateAsset</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$data</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Asset</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="8 tests cover line 33" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$asset</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">makeAssetFor</span><span class="keyword">(</span><span class="default">$type</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="8 tests cover line 34" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$asset</span><span class="default">-&gt;</span><span class="default">assigned_to_id</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="8 tests cover line 36" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$assetData</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'asset'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 37" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$asset</span><span class="default">-&gt;</span><span class="default">fill</span><span class="keyword">(</span><span class="default">$assetData</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="8 tests cover line 40" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$content</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'data'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$asset</span><span class="default">-&gt;</span><span class="default">content</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">create</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'language_code'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'language'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'en-EN'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'data'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$content</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="8 tests cover line 47" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$asset</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">authorize</span><span class="keyword">(</span><span class="default">Request</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Editorial</span><span class="default">&nbsp;</span><span class="default">$editorial</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 52" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">rules</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 57" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 58" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'language'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'sometimes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'string'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Rule</span><span class="default">::</span><span class="default">in</span><span class="keyword">(</span><span class="default">LanguageHelper</span><span class="default">::</span><span class="default">language_codes</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'code'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 59" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'required'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'array'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'min:1'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 60" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.type'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'required'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'string'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Rule</span><span class="default">::</span><span class="default">in</span><span class="keyword">(</span><span class="default">AssetType</span><span class="default">::</span><span class="default">cases</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 61" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'required'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'string'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'max:255'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 62" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.slug'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'sometimes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'string'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'max:255'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 63" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.description'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'sometimes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'string'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'max:255'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 64" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.url'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'sometimes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'url'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'max:255'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 65" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.schema_version'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'sometimes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'string'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'max:255'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 66" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.data'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'sometimes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'json'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 67" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets.*.assignee'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'required'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'integer'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'exists:users,id'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 68" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">prepareForValidation</span><span class="keyword">(</span><span class="default">ActionRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 73" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$assets</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">assets</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 75" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$assets</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$key</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$asset</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 76" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$data</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$asset</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'data'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$asset</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">...</span><span class="default">$asset</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'data'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$assets</span><span class="keyword">[</span><span class="default">$key</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$asset</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 85" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">merge</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 86" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'assets'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$assets</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 87" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">asController</span><span class="keyword">(</span><span class="default">ActionRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Editorial</span><span class="default">&nbsp;</span><span class="default">$editorial</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Collection</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 92" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$data</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">validated</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 94" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$assets</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">collect</span><span class="keyword">(</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'assets'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 95" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newAssets</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">collect</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 98" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 99" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$assets</span><span class="default">-&gt;</span><span class="default">each</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$asset</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$editorial</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$newAssets</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 100" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$editorial</span><span class="default">-&gt;</span><span class="default">hasAssetsOfType</span><span class="keyword">(</span><span class="default">$asset</span><span class="keyword">[</span><span class="default">'type'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">AssetExistsException</span><span class="keyword">(</span><span class="default">sprintf</span><span class="keyword">(</span><span class="default">'The&nbsp;editorial&nbsp;already&nbsp;has&nbsp;1&nbsp;%s&nbsp;asset.'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$asset</span><span class="keyword">[</span><span class="default">'type'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 104" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newAsset</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">$asset</span><span class="keyword">[</span><span class="default">'type'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$asset</span><span class="keyword">[</span><span class="default">'assignee'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 105" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'language'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'language'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'en-EN'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 106" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'data'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">data_get</span><span class="keyword">(</span><span class="default">$asset</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'data'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 107" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'asset'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">collect</span><span class="keyword">(</span><span class="default">$asset</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">only</span><span class="keyword">(</span><span class="default">'name'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'description'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'url'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'schema_version'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 108" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 110" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$editorial</span><span class="default">-&gt;</span><span class="default">addAsset</span><span class="keyword">(</span><span class="default">$newAsset</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 111" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newAssets</span><span class="default">-&gt;</span><span class="default">push</span><span class="keyword">(</span><span class="default">$newAsset</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 112" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 114" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$editorial</span><span class="default">-&gt;</span><span class="default">persist</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 116" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newAssets</span><span class="default">-&gt;</span><span class="default">each</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$asset</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 117" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">AssetCreated</span><span class="default">::</span><span class="default">dispatch</span><span class="keyword">(</span><span class="default">$asset</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 118" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 120" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">AssetExistsException</span><span class="default">&nbsp;</span><span class="default">$th</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">abort</span><span class="keyword">(</span><span class="default">500</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$th</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">\Throwable</span><span class="default">&nbsp;</span><span class="default">$th</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">$th</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 128" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$newAssets</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">jsonResponse</span><span class="keyword">(</span><span class="default">Collection</span><span class="default">&nbsp;</span><span class="default">$assets</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">AnonymousResourceCollection</span><span class="keyword">|</span><span class="default">AssetsResource</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 133" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$assets</span><span class="default">-&gt;</span><span class="default">count</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">1</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 134" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">AssetsResource</span><span class="default">::</span><span class="default">make</span><span class="keyword">(</span><span class="default">$assets</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">refresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">AssetsResource</span><span class="default">::</span><span class="default">collection</span><span class="keyword">(</span><span class="default">$assets</span><span class="default">-&gt;</span><span class="default">map</span><span class="default">-&gt;</span><span class="default">refresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">makeAssetFor</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$roleOrType</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Asset</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">match</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$roleOrType</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="8 tests cover line 143" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'story_editor'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">AssetType</span><span class="default">::</span><span class="default">Reporting</span><span class="default">-&gt;</span><span class="default">value</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">Reporting</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="3 tests cover line 144" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'photo_editor'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">AssetType</span><span class="default">::</span><span class="default">Photo</span><span class="default">-&gt;</span><span class="default">value</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">Photo</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 145" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'graphics_editor'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">AssetType</span><span class="default">::</span><span class="default">Graphic</span><span class="default">-&gt;</span><span class="default">value</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">Graphic</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="1 test covers line 146" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'illustrator'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">AssetType</span><span class="default">::</span><span class="default">Illustration</span><span class="default">-&gt;</span><span class="default">value</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">Illustration</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'headline_summary'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">AssetType</span><span class="default">::</span><span class="default">Headline_Summary</span><span class="default">-&gt;</span><span class="default">value</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">HeadlineSummary</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-end"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'fact_checker'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">AssetType</span><span class="default">::</span><span class="default">Fact</span><span class="default">-&gt;</span><span class="default">value</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">Fact</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-bs-title="8 tests cover line 150" data-bs-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_single_user_to_editorial&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_cannot_assign_multiple_assets_of_same_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_multiple_users_with_different_roles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialAssignmentsTest::it_can_assign_topline_editor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_a_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\EditorialsAssignmentTest::editor_can_assign_an_editorial_to_users&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialAssetCompleteWorkflowTest::it_runs_editorial_asset_complete_workflow&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows\EditorialEditingWorkflowTest::it_runs_editorial_workflow_over_multiple_weeks&lt;/li&gt;&lt;/ul&gt;" data-bs-placement="top" data-bs-html="true" class="col-1 text-end"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">default</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\InvalidArgumentException</span><span class="keyword">(</span><span class="default">'Cannot&nbsp;create&nbsp;asset&nbsp;for:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$roleOrType</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-end"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.8</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.16</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Thu Jan 23 11:19:27 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../../../_js/jquery.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../_js/bootstrap.bundle.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../../_js/file.js?v=11.0.8" type="text/javascript"></script>
 </body>
</html>
