<?php

namespace Database\Seeders\Crud;

use App\Domains\Topics\Models\Topic;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class TopicSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $topicsFile = app()->isLocal() ? 'topics_verticals_dev.json' : 'topics_verticals_prod.json';
        $topics = collect(File::json(resource_path('misc/' . $topicsFile)))->unique('Topic')->pluck('Topic');

        foreach ($topics as $topic) {
            Topic::factory()->create([
                'name' => $topic,
                'slug' => str($topic)->slug(),
                'active' => true,
                'icon' => 'pencil',
            ]);
        }

    }
}
