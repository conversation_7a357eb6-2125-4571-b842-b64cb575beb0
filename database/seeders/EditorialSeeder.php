<?php

namespace Database\Seeders;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Users\Models\CopyEditor;
use App\Domains\Users\Models\FactChecker;
use Illuminate\Database\Seeder;

class EditorialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pitch = Pitch::whereHas('state', function ($query): void {
            $query->where('state', PitchStage::Submitted);
        })->first();

        if (! $pitch) {
            return;
        }

        // $editor = $pitch->vertical->editor;

        // Editorial::factory()->create([
        //     'pitch_id' => $pitch->getKey(),
        //     'name' => str($pitch->short_name)->headline(),
        //     'phase' => EditorialPhase::Editing,
        //     'assigned_to_id' => $editor->getKey(),
        // ]);

        // $copyEditor = $editorial->copyEditor;
        // $factChecker = $editorial->factChecker;

        // $editorial = $editorial->first();

        // $task = new Task(
        //     userId: $factChecker->getKey(),
        //     title: 'Prepare Copy Edit',
        //     description: 'Prepare copy edit',
        //     type: 'prepare_copy_edit',
        //     dueAt: now()->addDays(10),
        //     data: new TaskData(
        //         model: $editorial->getModel(),
        //         label: 'Prepare Copy Edit',
        //         properties: []
        //     )
        // );

        // $editorial->createTask($task);
    }
}
