<?php

namespace Database\Seeders;

use App\Domains\Shared\Enums\Permission;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission as ModelsPermission;
use Spatie\Permission\Models\Role;

class PermissionsUpdateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed permissions for roles
        $existingPermissions = ModelsPermission::pluck('name');
        $seedPermissions = collect(Permission::cases())->pluck('value');

        $diffedPermissions = $seedPermissions->diff($existingPermissions)->unique();

        $createdPermissions = $diffedPermissions->map(fn(string $permission) => ModelsPermission::firstOrCreate(['name' => $permission, 'guard_name' => config('auth.defaults.guard')]));

        Role::with('permissions')->get()->each->givePermissionTo([
            ...$createdPermissions  
        ]);

        $this->command->info('Permissions updated successfully');
    }
}
