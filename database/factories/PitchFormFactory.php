<?php

namespace Database\Factories;

use App\Domains\Betterflow\V1\Pitches\Models\PitchForm;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PitchForm>
 */
class PitchFormFactory extends Factory
{
    protected $model = PitchForm::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            //
        ];
    }
}
