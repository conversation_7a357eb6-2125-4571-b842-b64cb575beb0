<?php

use App\Domains\Users\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('editorials', function (Blueprint $table) {
            $table->foreignIdFor(User::class, 'assigned_to_id')->nullable()->references('id')->on('users')->nullOnDelete()->cascadeOnUpdate();
            $table->foreignIdFor(User::class, 'topline_editor_id')->nullable()->references('id')->on('users')->nullOnDelete()->cascadeOnUpdate();
            $table->foreignIdFor(User::class, 'copy_editor_id')->nullable()->references('id')->on('users')->nullOnDelete()->cascadeOnUpdate();
            $table->foreignIdFor(User::class, 'fact_checker_id')->nullable()->references('id')->on('users')->nullOnDelete()->cascadeOnUpdate();
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('editorials', function (Blueprint $table) {
            $table->dropForeign(['assigned_to_id']);
            $table->dropForeign(['topline_editor_id']);
            $table->dropForeign(['copy_editor_id']);
            $table->dropForeign(['fact_checker_id']);
        });
    }
};
