<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove duplicate entries, keeping only the first occurrence
        DB::statement('
            DELETE FROM event_pitch e1
            WHERE EXISTS (
                SELECT 1 
                FROM event_pitch e2 
                WHERE e2.event_id = e1.event_id 
                  AND e2.pitch_id = e1.pitch_id 
                  AND e2.ctid > e1.ctid
            )
        ');

        Schema::table('event_pitch', function (Blueprint $table) {
            $table->unique(['event_id', 'pitch_id']);
        });
    }
};
