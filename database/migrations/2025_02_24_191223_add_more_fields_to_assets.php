<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assets', function (Blueprint $table) {
            $table->string('slug')->nullable();
            $table->text('description')->nullable();
            $table->string('url')->nullable();
            $table->boolean('required')->default(false);
            $table->string('schema_version')->default('1.0');
            $table->datetime('completed_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assets', function (Blueprint $table) {
            $table->dropColumn(['slug', 'description', 'url']);
            $table->dropColumn('required');
            $table->dropColumn('schema_version');
            $table->dropColumn('completed_at');
        });
    }
};
