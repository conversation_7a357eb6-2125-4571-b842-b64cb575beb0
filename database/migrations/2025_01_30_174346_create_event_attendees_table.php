<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_attendees', function (Blueprint $table) {
            $table->string('event_id')->constrained();
            $table->foreignId('user_id')
                ->constrained()
                ->references('id')
                ->on('users')
                ->cascadeOnDelete();

            $table->unique(['event_id', 'user_id']);
        });
    }
};
