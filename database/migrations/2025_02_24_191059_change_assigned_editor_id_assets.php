<?php

use App\Domains\Users\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assets', function (Blueprint $table) {
            // DB::transaction( function () {
            //     DB::raw('ALTER TABLE assets RENAME COLUMN assigned_editor_id TO assigned_to_id');
            // });
            $table->renameColumn('assigned_editor_id', 'assigned_to_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assets', function (Blueprint $table) {
            // DB::transaction( function () {
            //     DB::raw('ALTER TABLE assets RENAME COLUMN assigned_to_id TO assigned_editor_id');
            // });

            $table->renameColumn('assigned_to_id', 'assigned_editor_id');
        });
    }
};
