<?php

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_contents', function (Blueprint $table): void {
            $table->id();
            $table->foreignIdFor(Asset::class)->onDelete('cascade');
            $table->text('language_code')->default('en-EN');
            $table->json('data')->nullable();
            $table->unique(['asset_id', 'language_code']);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_contents');
    }
};
