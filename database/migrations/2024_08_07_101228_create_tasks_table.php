<?php

use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Users\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table): void {
            $table->id();
            $table->string('title')->default('');
            $table->text('description')->nullable();
            $table->foreignId('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->morphs('taskable');
            $table->string('status')->default(TaskStatus::Pending);
            $table->string('type');
            $table->string('progress')->default(0);
            $table->json('data')->nullable();
            $table->dateTimeTz('due_at')->nullable();
            $table->dateTimeTz('completed_at')->nullable();

            $table->timestampsTz();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
