<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pitch_forms', function (Blueprint $table): void {
            $table->id();
            $table->string('language_code')->default('en-EN');
            $table->foreignId('pitch_id')->references('id')->on('pitches')->cascadeOnDelete();
            $table->json('form')->nullable();
            $table->unique(['pitch_id', 'language_code']);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pitch_forms');
    }
};
