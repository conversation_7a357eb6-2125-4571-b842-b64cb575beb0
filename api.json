{"openapi": "3.1.0", "info": {"title": "Lighthouse API v.1", "version": "0.0.1", "description": "Lighthouse API documentation"}, "servers": [{"url": "https://api.lighthouse.test", "description": "Local"}, {"url": "https://api.lighthouse.dev.globalpressjournal.com", "description": "Development"}, {"url": "https://api.lighthouse.globalpressjournal.com", "description": "Production"}], "security": [{"http": []}], "paths": {"/admin/pitch/reasons": {"get": {"operationId": "admin.crud.pitch.reasons.index", "summary": "Display a listing of the resource", "tags": ["Admin / Pitch Hold Reasons", "PitchHoldReasons"], "responses": {"200": {"description": "Array of `CrudResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CrudResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "admin.crud.pitch.reasons.store", "summary": "Store a newly created resource in storage", "tags": ["Admin / Pitch Hold Reasons", "PitchHoldReasons"], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/pitch/reasons/{reason}": {"get": {"operationId": "admin.crud.pitch.reasons.show", "summary": "Display the specified resource", "tags": ["Admin / Pitch Hold Reasons", "PitchHoldReasons"], "parameters": [{"name": "reason", "in": "path", "required": true, "description": "The reason ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "admin.crud.pitch.reasons.update", "summary": "Update the specified resource in storage", "tags": ["Admin / Pitch Hold Reasons", "PitchHoldReasons"], "parameters": [{"name": "reason", "in": "path", "required": true, "description": "The reason ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "admin.crud.pitch.reasons.destroy", "summary": "Remove the specified resource from storage", "tags": ["Admin / Pitch Hold Reasons", "PitchHoldReasons"], "parameters": [{"name": "reason", "in": "path", "required": true, "description": "The reason ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": ""}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/pitch/stages": {"get": {"operationId": "admin.crud.pitch.stages.index", "summary": "Display a listing of the resource", "tags": ["Admin / Pitch Stages", "PitchStages"], "responses": {"200": {"description": "Array of `CrudResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CrudResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "admin.crud.pitch.stages.store", "summary": "Store a newly created resource in storage", "tags": ["Admin / Pitch Stages", "PitchStages"], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/pitch/stages/{stage}": {"get": {"operationId": "admin.crud.pitch.stages.show", "summary": "Display the specified resource", "tags": ["Admin / Pitch Stages", "PitchStages"], "parameters": [{"name": "stage", "in": "path", "required": true, "description": "The stage ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "admin.crud.pitch.stages.update", "summary": "Update the specified resource in storage", "tags": ["Admin / Pitch Stages", "PitchStages"], "parameters": [{"name": "stage", "in": "path", "required": true, "description": "The stage ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "admin.crud.pitch.stages.destroy", "summary": "Remove the specified resource from storage", "tags": ["Admin / Pitch Stages", "PitchStages"], "parameters": [{"name": "stage", "in": "path", "required": true, "description": "The stage ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": ""}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/pitch/types": {"get": {"operationId": "admin.crud.pitch.types.index", "summary": "Display a listing of the resource", "tags": ["Admin / Pitch Types", "PitchTypes"], "responses": {"200": {"description": "Array of `CrudResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CrudResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "admin.crud.pitch.types.store", "summary": "Store a newly created resource in storage", "tags": ["Admin / Pitch Types", "PitchTypes"], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/pitch/types/{type}": {"get": {"operationId": "admin.crud.pitch.types.show", "summary": "Display the specified resource", "tags": ["Admin / Pitch Types", "PitchTypes"], "parameters": [{"name": "type", "in": "path", "required": true, "description": "The type ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "admin.crud.pitch.types.update", "summary": "Update the specified resource in storage", "tags": ["Admin / Pitch Types", "PitchTypes"], "parameters": [{"name": "type", "in": "path", "required": true, "description": "The type ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`CrudResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrudResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "admin.crud.pitch.types.destroy", "summary": "Remove the specified resource from storage", "tags": ["Admin / Pitch Types", "PitchTypes"], "parameters": [{"name": "type", "in": "path", "required": true, "description": "The type ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": ""}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/topics": {"get": {"operationId": "admin.crud.topics.index", "summary": "Display a listing of topics", "tags": ["Admin / Topics", "Topics"], "responses": {"200": {"description": "Array of `TopicResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TopicResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "admin.crud.topics.store", "summary": "Store a topic", "tags": ["Admin / Topics", "Topics"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreTopicRequest"}}}}, "responses": {"200": {"description": "`TopicResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TopicResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/admin/topics/{topic}": {"get": {"operationId": "admin.crud.topics.show", "summary": "Display topic", "tags": ["Admin / Topics", "Topics"], "parameters": [{"name": "topic", "in": "path", "required": true, "description": "The topic ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`TopicResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TopicResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "admin.crud.topics.update", "summary": "Update topic", "tags": ["Admin / Topics", "Topics"], "parameters": [{"name": "topic", "in": "path", "required": true, "description": "The topic ID", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTopicRequest"}}}}, "responses": {"200": {"description": "`TopicResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TopicResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}, "delete": {"operationId": "admin.crud.topics.destroy", "summary": "Delete topic", "tags": ["Admin / Topics", "Topics"], "parameters": [{"name": "topic", "in": "path", "required": true, "description": "The topic ID", "schema": {"type": "integer"}}], "responses": {"204": {"description": "No content"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users": {"get": {"operationId": "admin.users.index", "summary": "List all users", "tags": ["Admin / Users", "Users"], "responses": {"200": {"description": "Array of `UsersResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UsersResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "admin.users.store", "summary": "Create a new user", "tags": ["Admin / Users", "Users"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/UsersResource"}}, "required": ["user"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}, "delete": {"operationId": "admin.users.delete", "summary": "Delete multiple users", "tags": ["Admin / Users", "BulkDeleteUsers"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"deleted": {"type": "boolean"}}, "required": ["deleted"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users/{user}": {"get": {"operationId": "admin.users.show", "summary": "Show a user", "tags": ["Admin / Users", "Users"], "parameters": [{"name": "user", "in": "path", "required": true, "description": "The user ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/UsersResource"}}, "required": ["user"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "admin.users.update", "description": "⚠️Cannot generate request documentation: Attempt to read property \"id\" on null", "summary": "Update user", "tags": ["Admin / Users", "Users"], "parameters": [{"name": "user", "in": "path", "required": true, "description": "The user ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/UsersResource"}}, "required": ["user"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}, "delete": {"operationId": "admin.users.destroy", "summary": "Delete user", "tags": ["Admin / Users", "Users"], "parameters": [{"name": "user", "in": "path", "required": true, "description": "The user ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": ""}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users/deactivate": {"post": {"operationId": "admin.users.deactivate", "summary": "Deactivates multiple users", "tags": ["Admin / Users", "UserBulkMethods"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users/activate": {"post": {"operationId": "admin.users.activate", "summary": "Activates multiple users", "tags": ["Admin / Users", "UserBulkMethods"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users/bulk/delete": {"delete": {"operationId": "admin.users.bulk.delete", "summary": "Delete multiple users", "tags": ["Admin / Users", "BulkDeleteUsers"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"deleted": {"type": "boolean"}}, "required": ["deleted"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users/bulk/deactivate": {"post": {"operationId": "admin.users.bulk.deactivate", "summary": "Deactivates multiple users", "tags": ["Admin / Users", "UserBulkMethods"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users/bulk/activate": {"post": {"operationId": "admin.users.bulk.activate", "summary": "Activates multiple users", "tags": ["Admin / Users", "UserBulkMethods"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/verticals": {"get": {"operationId": "admin.crud.verticals.index", "summary": "Display a listing of the resource", "tags": ["Admin / Verticals", "Verticals"], "responses": {"200": {"description": "Array of `VerticalsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VerticalsResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "admin.crud.verticals.store", "summary": "Store a newly created resource in storage", "tags": ["Admin / Verticals", "Verticals"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreVerticalRequest"}}}}, "responses": {"200": {"description": "`VerticalsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerticalsResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/admin/verticals/{vertical}": {"get": {"operationId": "admin.crud.verticals.show", "summary": "Display the specified resource", "tags": ["Admin / Verticals", "Verticals"], "parameters": [{"name": "vertical", "in": "path", "required": true, "description": "The vertical ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`VerticalsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerticalsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "admin.crud.verticals.update", "summary": "Update the specified resource in storage", "tags": ["Admin / Verticals", "Verticals"], "parameters": [{"name": "vertical", "in": "path", "required": true, "description": "The vertical ID", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVerticalsRequest"}}}}, "responses": {"200": {"description": "`VerticalsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerticalsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}, "delete": {"operationId": "admin.crud.verticals.destroy", "summary": "Remove the specified resource from storage", "tags": ["Admin / Verticals", "Verticals"], "parameters": [{"name": "vertical", "in": "path", "required": true, "description": "The vertical ID", "schema": {"type": "integer"}}], "responses": {"204": {"description": "No content", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/notifications": {"get": {"operationId": "notifications.index", "summary": "Get all notifications for the current user", "tags": ["Application / Notifications", "Notifications"], "responses": {"200": {"description": "Array of `NotificationsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationsResource"}}}}}}}}, "/notifications/mark-all-read": {"post": {"operationId": "notifications.mark-all-read", "summary": "Mark all notifications as read for the current user", "tags": ["Application / Notifications", "Notifications"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"notifications": {"type": ["array", "null"], "items": {"type": "string", "format": "uuid"}}}}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"error": {"type": "boolean"}, "message": {"type": "string", "example": "No notifications found"}}, "required": ["error", "message"]}, {"type": "object", "properties": {"error": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["error", "message"]}]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"message": {"type": "string", "example": "All notifications marked as read"}}, "required": ["message"]}, {"type": "object", "properties": {"message": {"type": "string", "example": "Marked all unread notifications read."}}, "required": ["message"]}]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/notifications/{notification}/toggle-read": {"put": {"operationId": "notifications.toggle-read", "summary": "Toggle the read status of a notification", "tags": ["Application / Notifications", "Notifications"], "parameters": [{"name": "notification", "in": "path", "required": true, "description": "The notification to update", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return a JSON response with the result of the update", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}}}}, "/users": {"get": {"operationId": "users.index", "summary": "Get all users", "tags": ["Application / Users", "Users"], "parameters": [{"name": "grouped", "in": "query", "schema": {"type": "boolean"}}, {"name": "online", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "`JsonResource`", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/JsonResource"}, {"type": "object"}]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/approve-travel": {"put": {"operationId": "betterflow.v1.pitches.actions.approve-travel", "tags": ["ApproveTravel"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pitch"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{editorial}/assets": {"get": {"operationId": "betterflow.v1.editorials.assets.index", "tags": ["Assets"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "Array of `AssetsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssetsResource"}}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{editorial}/assets/{asset}": {"get": {"operationId": "betterflow.v1.editorials.assets.show", "tags": ["Assets"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}, {"name": "asset", "in": "path", "required": true, "description": "The asset slug", "schema": {"type": ["string", "null"]}}], "responses": {"200": {"description": "`AssetsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{editorial}/assets/{asset}/comments": {"get": {"operationId": "betterflow.v1.editorials.assets.comments.index", "tags": ["AssetsComments"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}, {"name": "asset", "in": "path", "required": true, "description": "The asset slug", "schema": {"type": ["string", "null"]}}], "responses": {"200": {"description": "Retrieve all comments for the asset and return them as a resource collection.\n\n\n\nArray of `CommentsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CommentsResource"}}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "betterflow.v1.editorials.assets.comments.store", "tags": ["AssetsComments"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}, {"name": "asset", "in": "path", "required": true, "description": "The asset slug", "schema": {"type": ["string", "null"]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreCommentRequest"}}}}, "responses": {"200": {"description": "Return the newly created comment resource.\n\n\n\n`CommentsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/betterflow/v1/attachments/{attachment}": {"put": {"operationId": "betterflow.v1.attachments.update", "tags": ["Attachments"], "parameters": [{"name": "attachment", "in": "path", "required": true, "description": "The attachment ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "betterflow.v1.attachments.delete", "tags": ["Attachments"], "parameters": [{"name": "attachment", "in": "path", "required": true, "description": "The attachment ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/me": {"get": {"operationId": "users.me", "description": "This endpoint returns the authenticated user as a JSON response.", "summary": "Retrieve the authenticated user", "tags": ["Authentication", "User"], "responses": {"200": {"description": "Retrieve the authenticated user from the request.\nThen, transform the user into an authenticated user resource.\nFinally, return the authenticated user resource.\n\n\n\n`AuthenticatedUserResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticatedUserResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/auth/login": {"post": {"operationId": "auth.login", "summary": "<PERSON><PERSON>", "tags": ["Authentication", "<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "If the user doesn't exist or the password is incorrect, return an error message", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"message": {"type": "string", "example": "Unauthorized, check your credentials."}}, "required": ["message"]}, {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/AuthenticatedUserResource"}, "two_factor": {"type": "string"}, "message": {"type": "string", "example": "Successfully logged in!"}, "token": {"type": "string"}}, "required": ["user", "two_factor", "message", "token"]}]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/auth/register": {"post": {"operationId": "auth.register", "summary": "Register new user", "tags": ["Authentication", "RegisteredUser"], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}}, "required": ["token"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/auth/logout": {"post": {"operationId": "auth.logout", "summary": "Logout", "tags": ["Authentication", "<PERSON><PERSON>"], "responses": {"200": {"description": "Return a success message", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "You have been successfully logged out."}}, "required": ["message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/forgot-password": {"post": {"operationId": "password.email", "summary": "Handle an incoming password reset link request", "tags": ["Authentication", "PasswordResetLink"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}}, "required": ["email"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}, "required": ["status"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/reset-password": {"post": {"operationId": "password.reset", "summary": "Handle an incoming new password request", "tags": ["Authentication", "NewPassword"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "password_confirmation": {"type": "string"}}, "required": ["token", "email", "password", "password_confirmation"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}, "required": ["status"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/email/verification-notification": {"post": {"operationId": "verification.send", "summary": "Send a new email verification notification", "tags": ["Authentication", "EmailVerificationNotification"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/email/verify/{id}/{hash}": {"get": {"operationId": "verification.verify", "summary": "Mark the authenticated user's email address as verified", "tags": ["Authentication", "VerifyEmail"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "hash", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Email verified."}}, "required": ["message"]}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Already verified."}}, "required": ["message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/broadcasting/auth": {"get": {"operationId": "broadcast.authenticate", "summary": "Authenticate the request for channel access", "tags": ["Broadcast"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/betterflow/v1/comments/{comment}/toggle-like": {"put": {"operationId": "betterflow.v1.comments.toggle-like", "summary": "Toggles the like status of a comment for the authenticated user", "tags": ["Comments", "CommentToggleLike"], "parameters": [{"name": "comment", "in": "path", "required": true, "description": "The comment to toggle the like status for.", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/comments/{comment}/reply": {"post": {"operationId": "betterflow.v1.comments.reply", "summary": "Store a new comment reply", "tags": ["Comments", "CommentReply"], "parameters": [{"name": "comment", "in": "path", "required": true, "description": "The comment to reply to.", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"comment": {"type": "string", "description": "Set the comment and parent ID."}}}}}}, "responses": {"200": {"description": "Return the newly created comment resource.\n\n\n\n`CommentsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{arguments}/assets/{asset}/assists/{task}/complete": {"put": {"operationId": "betterflow.v1.editorials.assets.assists.complete", "tags": ["CompleteAssist"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}, {"name": "asset", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "task", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{arguments}/assists/{task}/complete": {"put": {"operationId": "betterflow.v1.editorials.assists.complete", "tags": ["CompleteEditorialAssist"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}, {"name": "task", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/account/confirm/{id}/{hash}": {"post": {"operationId": "account.confirm", "tags": ["ConfirmAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "hash", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"password": {"type": "string"}, "password_confirmation": {"type": "string"}}, "required": ["password", "password_confirmation"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Account confirmed, you can now login."}}, "required": ["message"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/user/confirm-password": {"post": {"operationId": "password.confirm", "summary": "Confirm the user's password", "tags": ["ConfirmablePassword"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"password": {"type": "string"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/confirmed-password-status": {"get": {"operationId": "password.confirmation", "summary": "Get the password confirmation status", "tags": ["ConfirmedPasswordStatus"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"confirmed": {"type": "string"}}, "required": ["confirmed"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/confirmed-two-factor-authentication": {"post": {"operationId": "two-factor.confirm", "summary": "Enable two factor authentication for the user", "tags": ["ConfirmedTwoFactorAuthentication"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/countries": {"get": {"operationId": "admin.crud.countries.countries.index", "tags": ["Countries"], "responses": {"200": {"description": "Array of `CountryResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CountryResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "admin.crud.countries.countries.store", "tags": ["Countries"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCountryRequest"}}}}, "responses": {"200": {"description": "`CountryResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountryResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/admin/countries/{country}": {"get": {"operationId": "admin.crud.countries.countries.show", "tags": ["Countries"], "parameters": [{"name": "country", "in": "path", "required": true, "description": "The country ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "`CountryResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountryResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "admin.crud.countries.countries.update", "tags": ["Countries"], "parameters": [{"name": "country", "in": "path", "required": true, "description": "The country ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCountryRequest"}}}}, "responses": {"200": {"description": "`CountryResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountryResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}, "delete": {"operationId": "admin.crud.countries.countries.destroy", "tags": ["Countries"], "parameters": [{"name": "country", "in": "path", "required": true, "description": "The country ID", "schema": {"type": "integer"}}], "responses": {"204": {"description": "No content"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{arguments}/assets": {"post": {"operationId": "betterflow.v1.editorials.assets.store", "tags": ["CreateAsset"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/sanctum/csrf-cookie": {"get": {"operationId": "sanctum.csrf-cookie", "summary": "Return an empty response simply to trigger the storage of the CSRF cookie in the browser", "tags": ["CsrfCookie"], "responses": {"204": {"description": "No content", "content": {"application/json": {"schema": {"anyOf": [{"type": "string"}, {"type": "array", "items": {}}]}}}}}}}, "/betterflow/v1/editorials/{editorial}/assign": {"put": {"operationId": "betterflow.v1.editorials.assign", "summary": "Handle the incoming request", "tags": ["EditorialAssignemnts"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignEditorialRequest"}}}}, "responses": {"200": {"description": "`EditorialsResource`", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/EditorialsResource"}, {"type": "object"}]}}}}, "409": {"description": "An error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "500": {"description": "An error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/betterflow/v1/editorials/{editorial}/comments": {"get": {"operationId": "betterflow.v1.editorials.comments.index", "tags": ["EditorialComments"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "Retrieve all comments for the editorial and return them as a resource collection.\n\n\n\nArray of `CommentsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CommentsResource"}}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "betterflow.v1.editorials.comments.store", "tags": ["EditorialComments"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreCommentRequest"}}}}, "responses": {"200": {"description": "Return the newly created comment resource.\n\n\n\n`CommentsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/betterflow/v1/editorials": {"get": {"operationId": "betterflow.v1.editorials.index", "tags": ["Editorials"], "responses": {"200": {"description": "Array of `EditorialsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EditorialsResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "betterflow.v1.editorials.store", "tags": ["Editorials"]}}, "/betterflow/v1/editorials/{editorial}": {"get": {"operationId": "betterflow.v1.editorials.show", "tags": ["Editorials"], "parameters": [{"name": "editorial", "in": "path", "required": true, "description": "The editorial public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "`EditorialsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditorialsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/dashboard": {"get": {"operationId": "betterflow.v1.editorials.dashboard", "tags": ["EditorialsDashboard"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResource"}}, "editorials": {"type": "array", "items": {"$ref": "#/components/schemas/EditorialsResource"}}}, "required": ["tasks", "editorials"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/events": {"get": {"operationId": "events.index", "summary": "List all events", "tags": ["Events"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"events": {"type": "string"}}, "required": ["events"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "events.store", "summary": "Create new event", "tags": ["Events"], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}}, "required": ["status"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/v1/events/{id}": {"get": {"operationId": "events.show", "summary": "Show event", "tags": ["Events"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"event": {"type": "string"}, "pitches": {"type": "string"}}, "required": ["event", "pitches"]}}}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Event not found"}}, "required": ["message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "events.update", "summary": "Update event", "tags": ["Events"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}}, "required": ["status"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "events.destroy", "summary": "Delete event", "tags": ["Events"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}}, "required": ["status"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/tasks": {"get": {"operationId": "betterflow.v1.tasks.index", "tags": ["GetTasks"], "responses": {"200": {"description": "Array of `TaskResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/impersonate/{user}": {"get": {"operationId": "user.impersonate", "summary": "Impersonate user", "tags": ["Impersonation"], "parameters": [{"name": "user", "in": "path", "required": true, "description": "The user ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"requested_id": {"type": "string"}, "persona": {"$ref": "#/components/schemas/User"}, "impersonator": {"type": "string"}, "token": {"type": "string"}}, "required": ["requested_id", "persona", "impersonator", "token"]}, {"type": "boolean"}]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/impersonate/leave": {"get": {"operationId": "user.impersonate.leave", "summary": "Leave the impersonation", "tags": ["Impersonation"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"requested_id": {"type": "string"}, "persona": {"type": "string"}, "token": {"type": "string"}}, "required": ["requested_id", "persona", "token"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/password": {"put": {"operationId": "user-password.update", "summary": "Update the user's password", "tags": ["Password"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/forms": {"get": {"operationId": "betterflow.v1.pitches.forms.index", "tags": ["PitchForms"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, {"type": "string"}]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "betterflow.v1.pitches.forms.store", "tags": ["PitchForms"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"language_code": {"type": "string"}, "form": {"type": "array", "items": {"type": "string"}}}, "required": ["language_code", "form"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/forms/{form}": {"put": {"operationId": "betterflow.v1.pitches.forms.update", "tags": ["PitchForms"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}, {"name": "form", "in": "path", "required": true, "description": "The form ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "betterflow.v1.pitches.forms.destroy", "tags": ["PitchForms"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}, {"name": "form", "in": "path", "required": true, "description": "The form ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Pitch form deleted successfully"}}, "required": ["message"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches": {"get": {"operationId": "betterflow.v1.pitches.index", "tags": ["Pitches"], "parameters": [{"name": "grouped", "in": "query", "schema": {"type": ["boolean", "null"]}}, {"name": "drafts", "in": "query", "schema": {"type": ["boolean", "null"]}}, {"name": "collaborating", "in": "query", "schema": {"type": ["boolean", "null"]}}], "responses": {"200": {"description": "Array of `PitchesResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PitchesResource"}}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "betterflow.v1.pitches.create", "tags": ["Pitches"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StorePitchRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PitchesResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/betterflow/v1/pitches/{pitch}": {"get": {"operationId": "betterflow.v1.pitches.show", "tags": ["Pitches"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "`PitchesResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PitchesResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "put": {"operationId": "betterflow.v1.pitches.update.put", "description": "⚠️Cannot generate request documentation: Call to a member function getPublicKey() on null", "tags": ["Pitches"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"pitch": {"$ref": "#/components/schemas/PitchesResource"}}, "required": ["pitch"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}, "patch": {"operationId": "betterflow.v1.pitches.update.patch", "description": "⚠️Cannot generate request documentation: Call to a member function getPublicKey() on null", "tags": ["Pitches"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"pitch": {"$ref": "#/components/schemas/PitchesResource"}}, "required": ["pitch"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}, "delete": {"operationId": "betterflow.v1.pitches.delete", "tags": ["Pitches"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"deleted": {"type": "string"}}, "required": ["deleted"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/submit": {"put": {"operationId": "betterflow.v1.pitches.actions.submit", "tags": ["Pitches / Actions", "SubmitPitch"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"task": {"type": "integer", "enum": [""]}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/approve": {"put": {"operationId": "betterflow.v1.pitches.actions.approve", "tags": ["Pitches / Actions", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"task": {"type": "integer", "enum": [""]}}}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pitch"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/decline": {"put": {"operationId": "betterflow.v1.pitches.actions.decline", "tags": ["Pitches / Actions", "<PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"reason": {"type": "string", "maxLength": 255}, "task": {"type": "integer"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PitchesResource"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/schedule": {"put": {"operationId": "betterflow.v1.pitches.actions.schedule", "tags": ["Pitches / Actions", "PitchMeeting"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"event_id": {"type": "string"}, "task": {"type": "integer", "enum": [""]}}, "required": ["event_id"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/reschedule": {"delete": {"operationId": "betterflow.v1.pitches.actions.reschedule", "tags": ["Pitches / Actions", "PitchMeeting"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}, {"name": "event_id", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/put-on-hold": {"put": {"operationId": "betterflow.v1.pitches.actions.put-on-hold", "tags": ["Pitches / Actions", "PutPitchOnHold"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"until": {"type": "string"}, "reason_id": {"type": "integer"}, "reason": {"type": "string", "maxLength": 300}}, "required": ["until"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PitchesResource"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/await-travel-approval": {"put": {"operationId": "betterflow.v1.pitches.actions.await-travel-approval", "tags": ["Pitches / Actions", "AwaitTravel"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"hold_until": {"type": "string"}, "task": {"type": "integer", "enum": [""]}, "instructions": {"type": "string", "maxLength": 1000}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pitch"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/actions/return-for-revision": {"put": {"operationId": "betterflow.v1.pitches.actions.submit-for-revision", "tags": ["Pitches / Actions", "SubmitForRevision"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"reason": {"type": "string"}, "task": {"type": "integer", "enum": [""]}, "instructions": {"type": "string", "maxLength": 1000}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pitch"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/attachments": {"get": {"operationId": "betterflow.v1.pitches.attachments.index", "tags": ["Pitches / Attachments", "PitchesAttachments"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "Array of `AttachmentsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AttachmentsResource"}}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "betterflow.v1.pitches.attachments.update", "tags": ["Pitches / Attachments", "PitchesAttachments"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/StoreAttachmentsRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"attachments": {"type": "string"}}, "required": ["attachments"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}, "403": {"$ref": "#/components/responses/AuthorizationException"}}}}, "/betterflow/v1/pitches/{pitch}/attachments/bulk": {"delete": {"operationId": "betterflow.v1.pitches.attachments.bulk-delete", "tags": ["Pitches / Attachments", "PitchesAttachments"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}, {"name": "ids", "in": "query", "schema": {"type": "string"}}], "responses": {"204": {"description": "No content", "content": {"application/json": {"schema": {"anyOf": [{"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, {"type": "object", "properties": {"message": {"type": "string", "example": "No attachments selected"}}, "required": ["message"]}]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/collaborators": {"put": {"operationId": "betterflow.v1.pitches.collaborators.update", "tags": ["Pitches / Collaborators", "Collaborators"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"users": {"type": ["array", "null"], "items": {"type": "integer"}}}}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/comments/{comment}": {"post": {"operationId": "betterflow.v1.pitches.comments.reply", "summary": "Store a reply to a comment", "tags": ["Pitches / Comments", "PitchComments"], "parameters": [{"name": "comment", "in": "path", "required": true, "description": "The comment to reply to.", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Return the newly created comment resource.\n\n\n\n`CommentsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/comments": {"get": {"operationId": "betterflow.v1.pitches.comments.index", "description": "This method retrieves all comments associated with a pitch. It takes in a\nrequest object and a pitch object, and returns a resource collection that\ncontains all of the comments for the pitch.", "summary": "Retrieves all comments for a given pitch", "tags": ["Pitches / Comments", "PitchComments"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch object.", "schema": {"type": "string"}}], "responses": {"200": {"description": "Retrieve all comments for the pitch and return them as a resource collection.\n\n\n\nArray of `CommentsResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CommentsResource"}}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "betterflow.v1.pitches.comments.create", "description": "This function creates a new comment for a pitch. It takes in a request object\nthat contains the comment content and a pitch object that represents the\npitch to add the comment to. It returns a resource collection that contains\nthe newly created comment.", "summary": "Store a new comment for a pitch", "tags": ["Pitches / Comments", "PitchComments"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch object that the comment should be added to.", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return the newly created comment as a resource.\n\n\n\n`CommentsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentsResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/pitches/{pitch}/toggle-watch": {"put": {"operationId": "betterflow.v1.pitches.toggle-watch", "tags": ["Pitches / Watchable", "Watchable<PERSON><PERSON>"], "parameters": [{"name": "pitch", "in": "path", "required": true, "description": "The pitch public id", "schema": {"type": "string"}}], "responses": {"200": {"description": "`PitchesResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PitchesResource"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/two-factor-recovery-codes": {"get": {"operationId": "two-factor.recovery-codes", "summary": "Get the two factor authentication recovery codes for authenticated user", "tags": ["RecoveryCode"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "recoveryCode.store", "summary": "Generate a fresh set of two factor authentication recovery codes", "tags": ["RecoveryCode"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{arguments}/assets/{asset}/request-assist": {"post": {"operationId": "betterflow.v1.editorials.assets.request-assist", "tags": ["RequestAssetAssist"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}, {"name": "asset", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Assistance requested."}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{arguments}/assets/{asset}/assign": {"post": {"operationId": "betterflow.v1.editorials.assets.assign-task", "tags": ["RequestAssetAssist"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}, {"name": "asset", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Assistance requested."}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{arguments}/request-assist": {"post": {"operationId": "betterflow.v1.editorials.editorial.request-assist", "tags": ["RequestEditorialAssist"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Assistance requested."}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/spaces": {"get": {"operationId": "spaces.index", "tags": ["Space"], "responses": {"200": {"description": "Array of `Space`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Space"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "spaces.store", "tags": ["Space"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "permissions": {"type": "string"}}}}}}, "responses": {"200": {"description": "Create a setting for the space\n$this->settingService->create('space_'.$space->id, json_encode($space));", "content": {"application/json": {"schema": {"type": "object", "properties": {"space": {"$ref": "#/components/schemas/App.Domains.Spaces.Models.Space"}, "message": {"type": "string", "example": "Space created successfully!"}}, "required": ["space", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/spaces/{space}": {"get": {"operationId": "spaces.show", "tags": ["Space"], "parameters": [{"name": "space", "in": "path", "required": true, "description": "The space ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/App.Domains.Spaces.Models.Space"}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/spaces/switch/{space}": {"post": {"operationId": "spaces.switch", "tags": ["Space"], "parameters": [{"name": "space", "in": "path", "required": true, "description": "The space slug", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Successfully switched to space."}, "space": {"$ref": "#/components/schemas/App.Domains.Spaces.Models.Space"}}, "required": ["message", "space"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/spaces/spaces/current": {"get": {"operationId": "spaces.current", "tags": ["Space"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"space": {"type": "string"}}, "required": ["space"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/two-factor-challenge": {"post": {"operationId": "twoFactorAuthenticatedSession.store", "summary": "Attempt to authenticate a new session using the two factor authentication code", "tags": ["TwoFactorAuthenticatedSession"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/user/two-factor-authentication": {"post": {"operationId": "two-factor.enable", "summary": "Enable two factor authentication for the user", "tags": ["TwoFactorAuthentication"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"force": {"type": "boolean", "default": false}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "two-factor.disable", "summary": "Disable two factor authentication for the user", "tags": ["TwoFactorAuthentication"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/two-factor-qr-code": {"get": {"operationId": "two-factor.qr-code", "summary": "Get the SVG element for the user's two factor authentication QR code", "tags": ["TwoFactorQrCode"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"svg": {"type": "string"}, "url": {"type": "string"}}, "required": ["svg", "url"]}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/two-factor-secret-key": {"get": {"operationId": "two-factor.secret-key", "summary": "Get the current user's two factor authentication setup / secret key", "tags": ["TwoFactorSecretKey"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"secretKey": {"type": "string"}}, "required": ["secret<PERSON>ey"]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/editorials/{arguments}/assets/{asset}": {"put": {"operationId": "betterflow.v1.editorials.assets.update", "tags": ["UpdateAsset"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}, {"name": "asset", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "`AssetsResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsResource"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/betterflow/v1/tasks/{arguments}": {"put": {"operationId": "betterflow.v1.tasks.update", "tags": ["UpdateTask"], "parameters": [{"name": "arguments", "in": "path", "required": true, "schema": {"type": ["string", "null"]}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "task": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": ["string", "null"]}, "user_id": {"type": "integer"}, "taskable_type": {"type": "string"}, "taskable_id": {"type": "integer"}, "status": {"$ref": "#/components/schemas/TaskStatus"}, "type": {"type": "string"}, "notes": {"type": ["string", "null"]}, "progress": {"type": "string"}, "data": {"type": ["array", "null"], "items": {}}, "due_at": {"type": ["string", "null"], "format": "date-time"}, "completed_at": {"type": ["string", "null"], "format": "date-time"}, "requested_by_id": {"type": ["integer", "null"]}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}}, "required": ["id", "title", "description", "user_id", "taskable_type", "taskable_id", "status", "type", "notes", "progress", "data", "due_at", "completed_at", "requested_by_id", "created_at", "updated_at"]}}, "required": ["success", "task"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/profile-information": {"put": {"operationId": "profile-information.update", "summary": "Update the user's profile information", "tags": ["User Profile", "ProfileInformation"], "responses": {"200": {"description": "Return the appropriate response.", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/photos": {"post": {"operationId": "profile.photos.store", "summary": "Upload a profile photo for the authenticated user", "tags": ["User Profile", "UploadProfilePhotos"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"photo": {"type": "string", "format": "binary", "contentMediaType": "application/octet-stream"}}, "required": ["photo"]}}}}, "responses": {"200": {"description": "Return a JSON response with a success message and the URL of the uploaded photo.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Profile photo updated."}, "photo_url": {"type": "string"}}, "required": ["message", "photo_url"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/user/preferences": {"put": {"operationId": "preferences.update", "description": "This endpoint updates the authenticated user's preferences.", "summary": "Update the user's preferences", "tags": ["User Profile", "Preferences"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"preferences": {"type": "object", "properties": {"panels": {"type": ["array", "null"], "items": {"type": "string"}}, "pitchStageOrder": {"type": ["array", "null"], "items": {"type": "string"}}, "calendarTimezones": {"type": "array", "items": {"type": "string"}}, "calendarSettings": {"type": "array", "items": {"type": "string"}}}}}}}}}, "responses": {"200": {"description": "Return the updated user resource\n\n\n\n`AuthenticatedUserResource`", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticatedUserResource"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/my/pitches": {"get": {"operationId": "users.pitches", "tags": ["UserPitches"], "responses": {"200": {"description": "Array of `PitchesResource`", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PitchesResource"}}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/admin/users/invite": {"post": {"operationId": "admin.users.invite", "tags": ["UsersInvite"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}}, "required": ["email"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}}, "required": ["status"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/tinker": {"get": {"operationId": "webTinker.index", "tags": ["WebTinker"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}}, "post": {"operationId": "webTinker.execute", "tags": ["WebTinker"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}}, "required": ["code"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}}, "components": {"securitySchemes": {"http": {"type": "http", "scheme": "bearer", "bearerFormat": ""}}, "schemas": {"ActivitiesResource": {"type": "object", "properties": {"id": {"type": "string"}, "caused_by": {"$ref": "#/components/schemas/SimpleUserResource"}, "what_happened": {"type": "object", "properties": {"activity": {"type": "string"}, "subject": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "changes": {"type": "string"}}, "required": ["id", "type", "changes"]}}, "required": ["activity", "subject"]}, "event": {"type": "string"}, "created_at": {"type": "string"}}, "required": ["id", "caused_by", "what_happened", "event", "created_at"], "title": "ActivitiesResource"}, "App.Domains.Betterflow.V1.Admin.Crud.Models.PitchStage": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": ["string", "null"]}, "slug": {"type": "string"}, "active": {"type": "boolean"}, "type": {"type": "string"}, "color": {"type": "string"}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}, "deleted_at": {"type": ["string", "null"], "format": "date-time"}}, "required": ["id", "name", "description", "slug", "active", "type", "color", "created_at", "updated_at", "deleted_at"], "title": "App.Domains.Betterflow.V1.Admin.Crud.Models.PitchStage"}, "App.Domains.Spaces.Models.Space": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "slug": {"type": "string"}, "description": {"type": "string"}, "permissions": {"type": ["string", "null"]}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}}, "required": ["id", "name", "slug", "description", "permissions", "created_at", "updated_at"], "title": "App.Domains.Spaces.Models.Space"}, "AssetsResource": {"type": "object", "properties": {"id": {"type": "string"}, "editorial_id": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "url": {"type": "string"}, "required": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "schema_version": {"type": "string"}, "assignee": {"$ref": "#/components/schemas/SimpleUserResource"}, "content": {"type": ["object", "null"], "properties": {"id": {"type": "string"}, "language": {"type": "string"}, "": {"type": "string"}}, "required": ["id", "language", null]}, "activities": {"type": "array", "items": {"$ref": "#/components/schemas/ActivitiesResource"}}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResource"}}}, "required": ["id", "name", "slug", "url", "required", "type", "description", "schema_version", "content"], "title": "AssetsResource"}, "AssignEditorialRequest": {"type": "object", "properties": {"topline_editor": {"type": "integer"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "role": {"type": "string", "enum": ["system", "super_admin", "admin", "audience_liaison", "copy_editor", "editor", "topic_editor", "vertical_editor", "story_editor", "web_editor", "research_editor", "fact_checker", "graphics_editor", "interpreter", "managing_editor", "photo_editor", "reporter", "editorial_admin", "researcher", "translator", "user", "illustrator", "photo_archivist", "topline_editor"]}, "asset": {"type": ["string", "null"], "enum": ["graphic", "illustration", "photo", "headline_summary", "reporting", "fact"]}}, "required": ["id"]}}}, "required": ["users"], "title": "AssignEditorialRequest"}, "AttachmentsResource": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "model_id": {"type": "string"}, "model_type": {"type": "string"}, "mime_type": {"type": "string"}, "size": {"type": "string"}, "original_path": {"type": "string"}, "conversions": {"type": "string"}, "full_url": {"type": "string"}, "original_url": {"type": "string"}, "preview_url": {"type": "string"}}, "required": ["id", "name", "model_id", "model_type", "mime_type", "size", "original_path", "conversions", "full_url", "original_url", "preview_url"], "title": "AttachmentsResource"}, "AuthenticatedUserResource": {"type": "object", "properties": {"id": {"type": "string"}, "active": {"type": "boolean"}, "bio": {"type": "string"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "has_session": {"type": "object", "properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "ip_address": {"type": "string"}, "user_agent": {"type": "string"}, "payload": {"type": "string"}, "last_activity": {"type": "object"}}, "required": ["id", "user_id", "ip_address", "user_agent", "payload", "last_activity"]}, "job_title": {"type": "string"}, "local_language": {"type": "string"}, "location": {"type": "string"}, "location_city": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "roles": {"type": "string"}, "permissions": {"type": "object", "properties": {"id": {"type": "integer"}, "can": {"type": "string"}, "guard": {"type": "string"}}, "required": ["id", "can", "guard"]}, "photo_url": {"type": "string"}, "slack_id": {"type": "string"}, "timezone": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResource"}}, "message": {"type": "null"}, "preferences": {"type": "object", "properties": {"panels": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "pitchStageOrder": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "slug": {"type": "string"}}, "required": ["id", "slug"]}}}, "required": ["panels", "pitchStageOrder"]}, "two_factor_enabled": {"type": "boolean"}, "two_factor": {"type": "object", "properties": {"recovery_codes": {"type": "string"}, "qr_code": {"type": "string"}}, "required": ["recovery_codes", "qr_code"]}}, "required": ["id", "active", "bio", "email", "email_verified", "has_session", "job_title", "local_language", "location", "location_city", "name", "roles", "permissions", "photo_url", "slack_id", "timezone", "preferences", "two_factor_enabled"], "title": "AuthenticatedUserResource"}, "CollaboratorResource": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "photo_url": {"type": "string"}, "has_session": {"type": "string", "description": "'role' => $this->whenLoaded('roles', $this->loadMissing('roles')->roles?->first()?->name),"}}, "required": ["id", "name", "photo_url", "has_session"], "title": "CollaboratorResource"}, "CommentsResource": {"type": "object", "properties": {"id": {"type": "string"}, "comment": {"type": "string"}, "user": {"$ref": "#/components/schemas/SimpleUserResource"}, "parent_id": {"type": "string"}, "commentable_type": {"type": "string"}, "commentable_id": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "replies": {"type": "string"}, "like_count": {"type": "string"}, "likes": {"type": "array", "items": {"$ref": "#/components/schemas/LikesResource"}}}, "required": ["id", "comment", "user", "parent_id", "commentable_type", "commentable_id", "created_at", "updated_at", "like_count"], "title": "CommentsResource"}, "CountryResource": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}}, "required": ["id", "name", "code"], "title": "CountryResource"}, "CreateCountryRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "code": {"type": "string", "maxLength": 2}}, "required": ["name", "code"], "title": "CreateCountryRequest"}, "CreateUserRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}, "bio": {"type": "string", "maxLength": 255}, "photo": {"type": "string"}, "job_title": {"type": "string", "maxLength": 255}, "local_language": {"type": "string", "maxLength": 255}, "location": {"type": "string", "maxLength": 255}, "slack_id": {"type": "string", "maxLength": 255}, "timezone": {"type": "string", "enum": ["Africa/Abidjan", "Africa/Accra", "Africa/Addis_Ababa", "Africa/Algiers", "Africa/Asmara", "Africa/Bamako", "Africa/Bangui", "Africa/Banjul", "Africa/Bissau", "Africa/Blantyre", "Africa/Brazzaville", "Africa/Bujumbura", "Africa/Cairo", "Africa/Casablanca", "Africa/Ceuta", "Africa/Conakry", "Africa/Dakar", "Africa/Dar_es_Salaam", "Africa/Djibouti", "Africa/Douala", "Africa/El_Aaiun", "Africa/Freetown", "Africa/Gaborone", "Africa/Harare", "Africa/Johannesburg", "Africa/Juba", "Africa/Kampala", "Africa/Khartoum", "Africa/Kigali", "Africa/Kinshasa", "Africa/Lagos", "Africa/Libreville", "Africa/Lome", "Africa/Luanda", "Africa/Lubumbashi", "Africa/Lusaka", "Africa/Malabo", "Africa/Maputo", "Africa/Maseru", "Africa/Mbabane", "Africa/Mogadishu", "Africa/Monrovia", "Africa/Nairobi", "Africa/Ndjamena", "Africa/Niamey", "Africa/Nouakchott", "Africa/Ouagadougou", "Africa/Porto-Novo", "Africa/Sao_Tome", "Africa/Tripoli", "Africa/Tunis", "Africa/Windhoek", "America/Adak", "America/Anchorage", "America/Anguilla", "America/Antigua", "America/Araguaina", "America/Argentina/Buenos_Aires", "America/Argentina/Catamarca", "America/Argentina/Cordoba", "America/Argentina/Jujuy", "America/Argentina/La_Rioja", "America/Argentina/Mendoza", "America/Argentina/Rio_Gallegos", "America/Argentina/Salta", "America/Argentina/San_Juan", "America/Argentina/San_Luis", "America/Argentina/Tucuman", "America/Argentina/Ushuaia", "America/Aruba", "America/Asuncion", "America/Atikokan", "America/Bahia", "America/Bahia_Banderas", "America/Barbados", "America/Belem", "America/Belize", "America/Blanc-Sablon", "America/Boa_Vista", "America/Bogota", "America/Boise", "America/Cambridge_Bay", "America/Campo_Grande", "America/Cancun", "America/Caracas", "America/Cayenne", "America/Cayman", "America/Chicago", "America/Chihuahua", "America/Ciudad_Juarez", "America/Costa_Rica", "America/Creston", "America/Cuiaba", "America/Curacao", "America/Danmarkshavn", "America/Dawson", "America/Dawson_Creek", "America/Denver", "America/Detroit", "America/Dominica", "America/Edmonton", "America/Eirunepe", "America/El_Salvador", "America/Fort_Nelson", "America/Fortaleza", "America/Glace_Bay", "America/Goose_Bay", "America/Grand_Turk", "America/Grenada", "America/Guadeloupe", "America/Guatemala", "America/Guayaquil", "America/Guyana", "America/Halifax", "America/Havana", "America/Hermosillo", "America/Indiana/Indianapolis", "America/Indiana/Knox", "America/Indiana/Marengo", "America/Indiana/Petersburg", "America/Indiana/Tell_City", "America/Indiana/Vevay", "America/Indiana/Vincennes", "America/Indiana/Winamac", "America/Inuvik", "America/Iqaluit", "America/Jamaica", "America/Juneau", "America/Kentucky/Louisville", "America/Kentucky/Monticello", "America/Kralendijk", "America/La_Paz", "America/Lima", "America/Los_Angeles", "America/Lower_Princes", "America/Maceio", "America/Managua", "America/Manaus", "America/Marigot", "America/Martinique", "America/Matamoros", "America/Mazatlan", "America/Menominee", "America/Merida", "America/Metlakatla", "America/Mexico_City", "America/Miquelon", "America/Moncton", "America/Monterrey", "America/Montevideo", "America/Montserrat", "America/Nassau", "America/New_York", "America/Nome", "America/Noronha", "America/North_Dakota/Beulah", "America/North_Dakota/Center", "America/North_Dakota/New_Salem", "America/Nuuk", "America/Ojinaga", "America/Panama", "America/Paramaribo", "America/Phoenix", "America/Port-au-Prince", "America/Port_of_Spain", "America/Porto_Velho", "America/Puerto_Rico", "America/Punta_Arenas", "America/Rankin_Inlet", "America/Recife", "America/Regina", "America/Resolute", "America/Rio_Branco", "America/Santarem", "America/Santiago", "America/Santo_Domingo", "America/Sao_Paulo", "America/Scoresbysund", "America/Sitka", "America/St_Barthelemy", "America/St_Johns", "America/St_Kitts", "America/St_Lucia", "America/St_Thomas", "America/St_Vincent", "America/Swift_Current", "America/Tegucigalpa", "America/Thule", "America/Tijuana", "America/Toronto", "America/Tortola", "America/Vancouver", "America/Whitehorse", "America/Winnipeg", "America/Yakutat", "Antarctica/Casey", "Antarctica/Davis", "Antarctica/DumontDUrville", "Antarctica/Macquarie", "Antarctica/Mawson", "Antarctica/McMurdo", "Antarctica/Palmer", "Antarctica/Rothera", "Antarctica/Syowa", "Antarctica/Troll", "Antarctica/Vostok", "Arctic/Longyearbyen", "Asia/Aden", "Asia/Almaty", "Asia/Amman", "Asia/Anadyr", "Asia/Aqtau", "Asia/Aqtobe", "Asia/Ashgabat", "Asia/Atyrau", "Asia/Baghdad", "Asia/Bahrain", "Asia/Baku", "Asia/Bangkok", "Asia/Barnaul", "Asia/Beirut", "Asia/Bishkek", "Asia/Brunei", "Asia/Chita", "Asia/Choibalsan", "Asia/Colombo", "Asia/Damascus", "Asia/Dhaka", "Asia/Dili", "Asia/Dubai", "Asia/Dushanbe", "Asia/Famagusta", "Asia/Gaza", "Asia/Hebron", "Asia/Ho_Chi_Minh", "Asia/Hong_Kong", "Asia/Hovd", "Asia/Irkutsk", "Asia/Jakarta", "Asia/Jayapura", "Asia/Jerusalem", "Asia/Kabul", "Asia/Kamchatka", "Asia/Karachi", "Asia/Kathmandu", "Asia/Khandyga", "Asia/Kolkata", "Asia/Krasnoyarsk", "Asia/Kuala_Lumpur", "Asia/Kuching", "Asia/Kuwait", "Asia/Macau", "Asia/Magadan", "Asia/Makassar", "Asia/Manila", "Asia/Muscat", "Asia/Nicosia", "Asia/Novokuznetsk", "Asia/Novosibirsk", "Asia/Omsk", "Asia/Oral", "Asia/Phnom_Penh", "Asia/Pontianak", "Asia/Pyongyang", "Asia/Qatar", "Asia/Qostanay", "Asia/Qyzylorda", "Asia/Riyadh", "Asia/Sakhalin", "Asia/Samarkand", "Asia/Seoul", "Asia/Shanghai", "Asia/Singapore", "Asia/Srednekolymsk", "Asia/Taipei", "Asia/Tashkent", "Asia/Tbilisi", "Asia/Tehran", "Asia/Thimphu", "Asia/Tokyo", "Asia/Tomsk", "Asia/Ulaanbaatar", "Asia/Urumqi", "Asia/Ust-Nera", "Asia/Vientiane", "Asia/Vladivostok", "Asia/Yakutsk", "Asia/Yangon", "Asia/Yekaterinburg", "Asia/Yerevan", "Atlantic/Azores", "Atlantic/Bermuda", "Atlantic/Canary", "Atlantic/Cape_Verde", "Atlantic/Faroe", "Atlantic/Madeira", "Atlantic/Reykjavik", "Atlantic/South_Georgia", "Atlantic/St_Helena", "Atlantic/Stanley", "Australia/Adelaide", "Australia/Brisbane", "Australia/Broken_Hill", "Australia/Darwin", "Australia/Eucla", "Australia/Hobart", "Australia/Lindeman", "Australia/Lord_Howe", "Australia/Melbourne", "Australia/Perth", "Australia/Sydney", "Europe/Amsterdam", "Europe/Andorra", "Europe/Astrakhan", "Europe/Athens", "Europe/Belgrade", "Europe/Berlin", "Europe/Bratislava", "Europe/Brussels", "Europe/Bucharest", "Europe/Budapest", "Europe/Busingen", "Europe/Chisinau", "Europe/Copenhagen", "Europe/Dublin", "Europe/Gibraltar", "Europe/Guernsey", "Europe/Helsinki", "Europe/Isle_of_Man", "Europe/Istanbul", "Europe/Jersey", "Europe/Kaliningrad", "Europe/Kirov", "Europe/Kyiv", "Europe/Lisbon", "Europe/Ljubljana", "Europe/London", "Europe/Luxembourg", "Europe/Madrid", "Europe/Malta", "Europe/Mariehamn", "Europe/Minsk", "Europe/Monaco", "Europe/Moscow", "Europe/Oslo", "Europe/Paris", "Europe/Podgorica", "Europe/Prague", "Europe/Riga", "Europe/Rome", "Europe/Samara", "Europe/San_Marino", "Europe/Sarajevo", "Europe/Saratov", "Europe/Simferopol", "Europe/Skopje", "Europe/Sofia", "Europe/Stockholm", "Europe/Tallinn", "Europe/Tirane", "Europe/Ulyanovsk", "Europe/Vaduz", "Europe/Vatican", "Europe/Vienna", "Europe/Vilnius", "Europe/Volgograd", "Europe/Warsaw", "Europe/Zagreb", "Europe/Zurich", "Indian/Antananarivo", "Indian/Chagos", "Indian/Christmas", "Indian/Cocos", "Indian/Comoro", "Indian/Kerguelen", "Indian/Mahe", "Indian/Maldives", "Indian/Mauritius", "Indian/Mayotte", "Indian/Reunion", "Pacific/Apia", "Pacific/Auckland", "Pacific/Bougainville", "Pacific/Chatham", "Pacific/Chuuk", "Pacific/Easter", "Pacific/Efate", "Pacific/Fakaofo", "Pacific/Fiji", "Pacific/Funafuti", "Pacific/Galapagos", "Pacific/Gambier", "Pacific/Guadalcanal", "Pacific/Guam", "Pacific/Honolulu", "Pacific/Kanton", "Pacific/Kiritimati", "Pacific/Kosrae", "Pacific/Kwajalein", "Pacific/Majuro", "Pacific/Marquesas", "Pacific/Midway", "Pacific/Nauru", "Pacific/Niue", "Pacific/Norfolk", "Pacific/Noumea", "Pacific/Pago_Pago", "Pacific/Palau", "Pacific/Pitcairn", "Pacific/Pohnpei", "Pacific/Port_Moresby", "Pacific/Rarotonga", "Pacific/Saipan", "Pacific/Tahiti", "Pacific/Tarawa", "Pacific/Tongatapu", "Pacific/Wake", "Pacific/Wallis", "UTC"]}, "active": {"type": "boolean"}, "primary_language": {"type": "string", "enum": ["en-EN", "fr-FR", "es-ES", "ne-NE", "ta-TA", "mn-MN", "ht-HT"]}, "roles": {"type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 1}}, "required": ["name", "email"], "title": "CreateUserRequest"}, "CrudResource": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "slug": {"type": "string"}, "description": {"type": "string"}, "active": {"type": "boolean"}, "color": {"type": "string"}, "icon": {"type": "string"}}, "required": ["id", "name", "slug", "description", "active", "color"], "title": "CrudResource"}, "EditorialsResource": {"type": "object", "properties": {"id": {"type": "string"}, "slug": {"type": "string"}, "type": {"type": "string"}, "current_owner": {"$ref": "#/components/schemas/SimpleUserResource"}, "assignee": {"$ref": "#/components/schemas/SimpleUserResource"}, "phase": {"type": "string"}, "phase_formatted": {"type": "string"}, "tasks": {"type": "string"}, "topic": {"type": "string"}, "pitch_id": {"type": "string"}, "short_name": {"type": "string"}, "vertical": {"type": "string"}, "reporter": {"$ref": "#/components/schemas/SimpleUserResource"}, "assigned_editor": {"$ref": "#/components/schemas/SimpleUserResource"}, "topline_editor": {"$ref": "#/components/schemas/SimpleUserResource"}, "copy_editor": {"$ref": "#/components/schemas/SimpleUserResource"}, "fact_checker": {"$ref": "#/components/schemas/SimpleUserResource"}, "assets": {"type": "array", "items": {"$ref": "#/components/schemas/AssetsResource"}}, "": {"type": "string"}}, "required": ["id", "slug", "type", "current_owner", "phase", "phase_formatted", "tasks", "topic", "pitch_id", "short_name", "vertical", null], "title": "EditorialsResource"}, "JsonResource": {"type": "string", "title": "JsonResource"}, "LikesResource": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/SimpleUserResource"}, "metadata": {"type": "string"}}, "required": ["user", "metadata"], "title": "LikesResource"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "space": {"type": ["string", "null"]}}, "required": ["email", "password"], "title": "LoginRequest"}, "NotificationUserResource": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "example": "user"}, "name": {"type": "string"}, "has_session": {"type": "string"}, "photo_url": {"type": "string"}}, "required": ["id", "type", "name", "has_session", "photo_url"], "title": "NotificationUserResource"}, "NotificationsResource": {"type": "object", "properties": {"id": {"type": "string"}, "read": {"type": "boolean"}, "": {"type": "string"}, "notification_type": {"type": "string"}, "to_whom": {"$ref": "#/components/schemas/NotificationUserResource"}, "created_at": {"type": "string"}, "created_at_format": {"type": "string"}}, "required": ["id", "read", null, "notification_type", "to_whom", "created_at", "created_at_format"], "title": "NotificationsResource"}, "Pitch": {"type": "object", "properties": {"id": {"type": "integer"}, "public_id": {"type": "string"}, "short_name": {"type": ["string", "null"]}, "slug": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "created_by": {"type": "integer"}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}, "deleted_at": {"type": ["string", "null"], "format": "date-time"}, "pitch_type": {"type": ["integer", "null"]}, "primary_language": {"type": ["string", "null"]}, "assigned_to": {"type": ["integer", "null"]}, "topic_id": {"type": ["integer", "null"]}, "vertical_id": {"type": ["integer", "null"]}, "form_schema_version": {"type": "string"}, "author": {"$ref": "#/components/schemas/User"}, "topic": {"$ref": "#/components/schemas/Topic"}, "vertical": {"$ref": "#/components/schemas/Vertical"}, "type": {"$ref": "#/components/schemas/PitchType"}, "state": {"$ref": "#/components/schemas/PitchState"}}, "required": ["id", "public_id", "short_name", "slug", "country_code", "created_by", "created_at", "updated_at", "deleted_at", "pitch_type", "primary_language", "assigned_to", "topic_id", "vertical_id", "form_schema_version"], "title": "Pitch"}, "PitchStage": {"type": "string", "enum": ["draft", "submitted", "approved", "declined", "on-hold", "return-for-revision", "pitch-meeting", "awaiting-travel-approval"], "title": "PitchStage"}, "PitchState": {"type": "object", "properties": {"id": {"type": "integer"}, "statable_type": {"type": "string"}, "statable_id": {"type": "integer"}, "state": {"$ref": "#/components/schemas/PitchStage"}, "attributes": {"type": ["array", "null"], "items": {}}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}, "stateModel": {"$ref": "#/components/schemas/App.Domains.Betterflow.V1.Admin.Crud.Models.PitchStage"}}, "required": ["id", "statable_type", "statable_id", "state", "attributes", "created_at", "updated_at"], "title": "PitchState"}, "PitchType": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "slug": {"type": "string"}, "description": {"type": ["string", "null"]}, "active": {"type": "boolean"}, "color": {"type": "string"}, "deleted_at": {"type": ["string", "null"], "format": "date-time"}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}, "icon": {"type": ["string", "null"]}}, "required": ["id", "name", "slug", "description", "active", "color", "deleted_at", "created_at", "updated_at", "icon"], "title": "PitchType"}, "PitchesResource": {"type": "object", "properties": {"id": {"type": "string"}, "assigned_to": {"$ref": "#/components/schemas/SimpleUserResource"}, "attachments_count": {"type": "string"}, "collaborators": {"type": "array", "items": {"$ref": "#/components/schemas/CollaboratorResource"}}, "comments_count": {"type": "string"}, "country": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}, "required": ["code", "name"]}, "created_by": {"$ref": "#/components/schemas/SimpleUserResource"}, "created_at": {"type": "string"}, "form_languages": {"type": "string"}, "form_schema_version": {"type": "string"}, "primary_language": {"type": "string"}, "revisions_count": {"type": "string"}, "short_name": {"type": "string"}, "scheduled_for_meeting": {"type": "string"}, "slug": {"type": "string"}, "stage": {"$ref": "#/components/schemas/StageResource"}, "topic": {"$ref": "#/components/schemas/TopicResource"}, "type": {"type": "string"}, "vertical": {"$ref": "#/components/schemas/VerticalsResource"}, "watchers_count": {"type": "string"}, "activities": {"type": "array", "items": {"$ref": "#/components/schemas/ActivitiesResource"}}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/AttachmentsResource"}}, "comments": {"type": "array", "items": {"$ref": "#/components/schemas/CommentsResource"}}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResource"}}, "watchers": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleUserResource"}}}, "required": ["id", "comments_count", "country", "created_at", "form_languages", "form_schema_version", "primary_language", "revisions_count", "short_name", "slug", "watchers_count"], "title": "PitchesResource"}, "Session": {"type": "object", "properties": {"id": {"type": "integer"}, "user_id": {"type": ["integer", "null"]}, "ip_address": {"type": ["string", "null"]}, "user_agent": {"type": ["string", "null"]}, "payload": {"type": "string"}, "last_activity": {"type": "integer"}}, "required": ["id", "user_id", "ip_address", "user_agent", "payload", "last_activity"], "title": "Session"}, "SimpleUserResource": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "photo_url": {"type": "string"}, "roles": {"type": "string"}, "has_session": {"type": "string"}, "job_title": {"type": "string"}}, "required": ["id", "name", "email", "photo_url", "roles", "has_session", "job_title"], "title": "SimpleUserResource"}, "Space": {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}, "permissions": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleUserResource"}}}, "required": ["name", "slug", "permissions", "description"], "title": "Space"}, "StageResource": {"type": "object", "properties": {"active": {"type": "string"}, "attributes": {"type": "string"}, "color": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}}, "required": ["active", "attributes", "color", "description", "name", "slug"], "title": "StageResource"}, "StoreAttachmentsRequest": {"type": "object", "properties": {"attachments": {"type": ["array", "null"], "items": {"type": "string", "format": "binary", "contentMediaType": "application/octet-stream"}}}, "title": "StoreAttachmentsRequest"}, "StoreCommentRequest": {"type": "object", "properties": {"comment": {"type": "string"}, "comment_id": {"type": "number"}}, "required": ["comment"], "title": "StoreCommentRequest"}, "StorePitchRequest": {"type": "object", "properties": {"working_headline": {"type": "string", "maxLength": 255}, "short_name": {"type": "string", "maxLength": 255}, "slug": {"type": "string", "maxLength": 255}, "form": {"type": "array", "items": {"type": "string"}}, "country_code": {"type": "string", "enum": ["AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "VA", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KP", "KR", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "SH", "KN", "LC", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "CS", "SC", "SL", "SG", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SR", "SJ", "SZ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "US", "UM", "UY", "UZ", "VU", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "pitch_type": {"type": "number"}, "assigned_to": {"type": "number"}, "topic": {"type": "number"}, "vertical": {"type": "number"}, "collaborators": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "language_code": {"type": "string", "description": "Pitch form", "enum": ["en-EN", "fr-FR", "es-ES", "ne-NE", "ta-TA", "mn-MN", "ht-HT"]}}, "required": ["slug", "topic"], "title": "StorePitchRequest"}, "StoreTopicRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "maxLength": 255}, "slug": {"type": "string", "maxLength": 255}, "color": {"type": "string", "maxLength": 7}, "active": {"type": "boolean"}, "icon": {"type": "string"}, "assigned_editor_id": {"type": "number", "enum": ["7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23"]}}, "required": ["name", "description", "slug", "color"], "title": "StoreTopicRequest"}, "StoreVerticalRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "maxLength": 255}, "slug": {"type": "string", "maxLength": 255}, "color": {"type": "string", "maxLength": 7}, "active": {"type": "boolean"}, "icon": {"type": "string"}, "topic": {"type": "number"}, "assigned_editor_id": {"type": "integer", "enum": ["7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23"]}}, "title": "StoreVerticalRequest"}, "TaskResource": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "completed": {"type": "string"}, "progress": {"type": "string"}, "last_updated_at": {"type": "string"}, "created_at": {"type": "string"}, "completed_at": {"type": "string"}, "due_at": {"type": "string"}, "assignee": {"$ref": "#/components/schemas/SimpleUserResource"}, "status": {"type": "string"}, "type": {"type": "string"}, "data": {"type": "object", "properties": {"subject_type": {"type": "string", "description": "'subject' => $subject,"}, "subject_id": {"anyOf": [{"type": "integer"}, {"type": "string"}]}}, "required": ["subject_type", "subject_id"]}, "notes": {"type": "string"}, "instructions": {"type": "string"}, "action": {"type": "string"}}, "required": ["id", "title", "description", "completed", "progress", "created_at", "due_at", "status", "type", "notes", "instructions", "action"], "title": "TaskResource"}, "TaskStatus": {"type": "string", "enum": ["pending", "completed", "on_hold", "failed", "canceled"], "title": "TaskStatus"}, "Topic": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "slug": {"type": "string"}, "color": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "active": {"type": "boolean"}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}, "assigned_editor_id": {"type": ["integer", "null"]}, "icon": {"type": ["string", "null"]}}, "required": ["id", "name", "slug", "color", "description", "active", "created_at", "updated_at", "assigned_editor_id", "icon"], "title": "Topic"}, "TopicResource": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "slug": {"type": "string"}, "description": {"type": "string"}, "active": {"type": "boolean"}, "color": {"type": "string"}, "icon": {"type": "string"}, "assigned_editor": {"$ref": "#/components/schemas/SimpleUserResource"}, "verticals": {"type": "array", "items": {"$ref": "#/components/schemas/CrudResource"}}}, "required": ["id", "name", "slug", "description", "active", "color"], "title": "TopicResource"}, "TwoFactorLoginRequest": {"type": "object", "properties": {"code": {"type": ["string", "null"]}, "recovery_code": {"type": ["string", "null"]}}, "title": "TwoFactorLoginRequest"}, "UpdateCountryRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "code": {"type": "string", "maxLength": 2}}, "required": ["name", "code"], "title": "UpdateCountryRequest"}, "UpdateTopicRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "maxLength": 300}, "color": {"type": "string", "minLength": 7, "maxLength": 7}, "active": {"type": "boolean"}, "icon": {"type": "string"}, "assigned_editor_id": {"type": "number", "enum": ["7", "12", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23"]}}, "title": "UpdateTopicRequest"}, "UpdateVerticalsRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "maxLength": 255}, "color": {"type": "string", "description": "'slug' => ['sometimes', 'string', 'max:255', 'unique:verticals,slug'],", "maxLength": 7}, "active": {"type": "boolean"}, "icon": {"type": "string"}, "assigned_editor_id": {"type": "integer", "enum": ["7", "12", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23"]}}, "title": "UpdateVerticalsRequest"}, "User": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "email": {"type": "string"}, "email_verified_at": {"type": ["string", "null"], "format": "date-time"}, "api_token": {"type": ["string", "null"]}, "photo_url": {"type": ["string", "null"]}, "active": {"type": "boolean"}, "job_title": {"type": ["string", "null"]}, "local_language": {"type": "string"}, "bio": {"type": ["string", "null"]}, "location": {"type": ["string", "null"]}, "location_city": {"type": ["string", "null"]}, "slack_id": {"type": ["string", "null"]}, "timezone": {"type": "string"}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}, "deleted_at": {"type": ["string", "null"], "format": "date-time"}, "type": {"type": ["string", "null"]}, "two_factor_secret": {"type": ["string", "null"]}, "two_factor_recovery_codes": {"type": ["string", "null"]}, "preferences": {"type": ["array", "null"], "items": {}}, "current_space_id": {"type": ["integer", "null"]}, "session": {"$ref": "#/components/schemas/Session"}}, "required": ["id", "name", "email", "email_verified_at", "api_token", "photo_url", "active", "job_title", "local_language", "bio", "location", "location_city", "slack_id", "timezone", "created_at", "updated_at", "deleted_at", "type", "two_factor_secret", "two_factor_recovery_codes", "preferences", "current_space_id"], "title": "User"}, "UsersResource": {"type": "object", "properties": {"id": {"type": "string"}, "active": {"type": "boolean"}, "bio": {"type": "string"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "has_session": {"type": "object", "properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "ip_address": {"type": "string"}, "user_agent": {"type": "string"}, "payload": {"type": "string"}, "last_activity": {"type": "object"}}, "required": ["id", "user_id", "ip_address", "user_agent", "payload", "last_activity"]}, "job_title": {"type": "string"}, "local_language": {"type": "string"}, "location": {"type": "string"}, "location_city": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "roles": {"type": "string"}, "permissions": {"type": "string"}, "photo_url": {"type": "string"}, "slack_id": {"type": "string"}, "timezone": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResource"}}}, "required": ["id", "active", "bio", "email", "email_verified", "has_session", "job_title", "local_language", "location", "location_city", "name", "roles", "permissions", "photo_url", "slack_id", "timezone"], "title": "UsersResource"}, "Vertical": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "slug": {"type": "string"}, "description": {"type": ["string", "null"]}, "color": {"type": "string"}, "active": {"type": "boolean"}, "assigned_editor_id": {"type": ["integer", "null"]}, "created_at": {"type": ["string", "null"], "format": "date-time"}, "updated_at": {"type": ["string", "null"], "format": "date-time"}, "deleted_at": {"type": ["string", "null"], "format": "date-time"}, "topic_id": {"type": ["integer", "null"]}, "icon": {"type": ["string", "null"]}}, "required": ["id", "name", "slug", "description", "color", "active", "assigned_editor_id", "created_at", "updated_at", "deleted_at", "topic_id", "icon"], "title": "Vertical"}, "VerticalsResource": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "slug": {"type": "string"}, "color": {"type": "string"}, "active": {"type": "string"}, "icon": {"type": "string"}, "topic": {"$ref": "#/components/schemas/TopicResource"}, "assigned_editor": {"$ref": "#/components/schemas/SimpleUserResource"}}, "required": ["id", "name", "description", "slug", "color", "active", "icon"], "title": "VerticalsResource"}}, "responses": {"AuthenticationException": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "ValidationException": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Errors overview."}, "errors": {"type": "object", "description": "A detailed description of each field that failed validation.", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "required": ["message", "errors"]}}}}, "AuthorizationException": {"description": "Authorization error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "ModelNotFoundException": {"description": "Not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}}}}