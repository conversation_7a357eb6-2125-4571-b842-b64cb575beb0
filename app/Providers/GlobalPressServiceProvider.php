<?php

namespace App\Providers;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator;
use App\Domains\Betterflow\V1\Pitches\Models\PitchForm;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Comments\Models\Comment;
use App\Domains\Shared\Bus\LogCommand;
use App\Domains\Shared\Bus\UseDatabaseTransactions;
use App\Domains\Shared\Models\CommentLike;
use App\Domains\Users\Models\Admin;
use App\Domains\Users\Models\AudienceLiaison;
use App\Domains\Users\Models\CopyEditor;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\EditorialAdmin;
use App\Domains\Users\Models\FactChecker;
use App\Domains\Users\Models\GraphicsEditor;
use App\Domains\Users\Models\Interpreter;
use App\Domains\Users\Models\ManagingEditor;
use App\Domains\Users\Models\PhotoEditor;
use App\Domains\Users\Models\Reporter;
use App\Domains\Users\Models\Researcher;
use App\Domains\Users\Models\SuperAdmin;
use App\Domains\Users\Models\System;
use App\Domains\Users\Models\Translator;
use App\Domains\Users\Models\User;
use App\Domains\Users\Policies\UserPolicy;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;
use Maize\Markable\Models\Like;

class GlobalPressServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void {}

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Model::shouldBeStrict(! app()->isProduction());
        Model::automaticallyEagerLoadRelationships();
        Model::unguard();

        // if (! app()->runningInConsole()) {
        //     Model::automaticallyEagerLoadRelationships();
        // }

        if (app()->isProduction()) {
            DB::prohibitDestructiveCommands();
        }

        if (! app()->runningUnitTests()) {
            Date::use(CarbonImmutable::class);
        }

        // Force https
        if (! app()->isLocal()) {
            URL::forceHttps();
        }

        // Validator for immutable dates
        Validator::extend('immutable_date', function ($attribute, $value, $parameters, $validator) {
            try {
                CarbonImmutable::parse($value);
                return true;
            } catch (\Exception $e) {
                return false;
            }
        });

        JsonResource::withoutWrapping();

        Relation::enforceMorphMap([
            'pitch' => Pitch::class,
            'pitch_form' => PitchForm::class,
            'asset' => Asset::class,
            'editorial' => Editorial::class,
            'editorial_package' => EditorialPackage::class,
            'task' => Task::class,
            'comment' => Comment::class,
            'like' => Like::class,
            'comment_like' => CommentLike::class,
            'pitch_collaborator' => PitchCollaborator::class,

            'system' => System::class,
            // Roles
            'super_admin' => SuperAdmin::class,
            'admin' => Admin::class,

            'managing_editor' => ManagingEditor::class,
            'editor' => Editor::class,
            'topic_editor' => Editor::class,
            'vertical_editor' => Editor::class,
            'story_editor' => Editor::class,
            'photo_editor' => PhotoEditor::class,
            'copy_editor' => CopyEditor::class,
            'graphics_editor' => GraphicsEditor::class,
            'web_editor' => Editor::class,

            'reporter' => Reporter::class,
            'editorial_admin' => EditorialAdmin::class,

            'audience_liaison' => AudienceLiaison::class,
            'fact_checker' => FactChecker::class,
            'researcher' => Researcher::class,
            'translator' => Translator::class,
            'interpreter' => Interpreter::class,

            'user' => User::class,
        ]);

        Collection::macro('aggregate', function ($column = 'name', $replacer = 'and') {
            /** @var Collection $this */
            return str($this->pluck($column)->implode(', '))->replaceLast(', ', sprintf(' %s ', $replacer));
        });

        Collection::macro('aggregateSimple', function ($replacer = 'and') {
            /** @var Collection $this */
            return str($this->implode(', '))->replaceLast(', ', sprintf(' %s ', $replacer));
        });

        SupportCollection::macro('aggregate', function ($column = 'name', $replacer = 'and') {
            /** @var Collection $this */
            return str($this->pluck($column)->implode(', '))->replaceLast(', ', sprintf(' %s ', $replacer));
        });

        Schema::defaultStringLength(191);

        Gate::before(function ($user, $ability): ?true {
            return $user->hasAnyRole('super_admin', 'admin', 'system') ? true : null;
        });

        Gate::policy(User::class, UserPolicy::class);

        Bus::pipeThrough([
            UseDatabaseTransactions::class,
            LogCommand::class,
        ]);

        UrlGenerator::macro('frontendUrlFromRoute', function ($name, $parameters = [], $absolute = true) {
            /** @var \Illuminate\Routing\UrlGenerator $this */
            $url = $this->route(
                name: $name,
                parameters: $parameters,
                absolute: $absolute,
            );

            $parts = parse_url($url);
            $scheme = $parts['scheme'];
            $host = $parts['host'];

            $replacementUrl = sprintf('%s://%s', $scheme, $host);

            return str($url)->replace($replacementUrl, config('app.frontend_url'));
        });

        UrlGenerator::macro('signedFrontendUrlFromRoute', function ($name, $parameters = [], $expiration = null, $absolute = true) {
            /** @var \Illuminate\Routing\UrlGenerator $this */
            $url = $this->signedRoute(
                name: $name,
                parameters: $parameters,
                expiration: $expiration,
                absolute: $absolute,
            );

            $parts = parse_url($url);
            $scheme = $parts['scheme'];
            $host = $parts['host'];

            $replacementUrl = sprintf('%s://%s', $scheme, $host);

            return str($url)->replace($replacementUrl, config('app.frontend_url'));
        });

        UrlGenerator::macro('signedUrl', function ($path, $parameters = [], $expiration = null, $absolute = true): string {

            $query = [];

            assert($this instanceof UrlGenerator);
            /** @param-closure-this */
            /** @var UrlGenerator $this */
            /**
             * @phpstan-ignore-next-line
             */
            $this->ensureSignedRouteParametersAreNotReserved(
                $parameters = Arr::wrap($parameters),
            );

            $url = $this->to($path, $parameters);

            if ($expiration) {
                /**
                 * @phpstan-ignore-next-line
                 */
                $query['expires'] = $this->availableAt($expiration);
            }

            ksort($parameters);

            /**
             * @phpstan-ignore-next-line
             */
            $key = call_user_func($this->keyResolver);

            $signature = hash_hmac(
                'sha256',
                $url,
                is_array($key) ? $key[0] : $key,
            );

            $query['signature'] = $signature;

            return $url . '?' . http_build_query($query);
        });

        UrlGenerator::macro('temporarySignedUrl', function ($path, $expiration, $parameters = [], $absolute = true) {
            /** @var UrlGenerator $this */
            $url = $this->signedUrl($path, $parameters, $expiration, $absolute);

            return $url;
        });
    }
}
