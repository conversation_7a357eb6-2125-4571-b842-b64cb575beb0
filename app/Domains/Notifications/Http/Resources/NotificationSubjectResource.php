<?php

namespace App\Domains\Notifications\Http\Resources;

use App\Domains\Shared\Contracts\HasSlug;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationSubjectResource extends JsonResource
{
    public function toArray(Request $request): array
    {

        $idAttributes = collect($this->resource->getAttributes())
            ->filter(fn($value, $key) => str_ends_with($key, '_id'))
            ->map(function ($value, $key) {
                try {
                    // Check if the relation exists and is loaded
                    if ($relation = $this->resource->getRelationForKey($key)) {
                        // If relation exists and has a public key, use that
                            return $relation->getResourceKey();
                    }

                    // Fallback to original value
                    return $value;
                } catch (\Exception $e) {
                    // Log the error or handle it silently
                    return $value;
                }
            })
            ->toArray();

        return [
            'id' => $this->getResourceKey(),
            'slug' => $this->when(
                $this->resource instanceof HasSlug,
                $this->resource->slug ?? null
            ),
            'type' => $this->resource->getMorphClass(),
            ...$idAttributes,
            ...$this->additional,
        ];
    }
}
