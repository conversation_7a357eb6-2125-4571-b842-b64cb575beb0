<?php

declare(strict_types=1);

namespace App\Domains\Users\Models;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationPitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Shared\Casts\Timezone;
use App\Domains\Shared\Enums\Permission;
use App\Domains\Shared\Models\BaseUser;
use App\Domains\Spaces\Models\Space;
use App\Domains\Users\Database\Factories\UserFactory;
use App\Domains\Users\Contracts\HasRolesAndPermissions;
use App\Domains\Users\Models\Concerns\HasRoles;
use App\Domains\Users\Observers\UserObserver;
use App\Domains\Users\ValueObjects\Location;
use GlobalPress\Events\Models\Event;
use GlobalPress\Events\Models\EventPitch;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\UploadedFile;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Sanctum\Contracts\HasApiTokens as HasApiTokensContract;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\NewAccessToken;
use Laravel\Sanctum\PersonalAccessToken;
use Maize\Markable\Models\Like;
use Parental\HasChildren;
use Spatie\Activitylog\Traits\CausesActivity;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[ObservedBy(UserObserver::class)]
class User extends BaseUser implements HasApiTokensContract, HasMedia, HasRolesAndPermissions
{
    use CausesActivity;
    use HasApiTokens;
    use HasChildren;
    use HasFactory;
    use HasRoles;
    use InteractsWithMedia;
    use Notifiable;
    use SoftDeletes;
    use TwoFactorAuthenticatable;

    protected $guard_name = 'api';

    protected $fillable = [
        'active',
        'bio',
        'current_space_id',
        'email_verified_at',
        'email',
        'job_title',
        'local_language',
        'location',
        'name',
        'password',
        'photo_url',
        'preferences',
        'slack_id',
        'timezone',
        'topic_id',
        'type',
        'vertical',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'api_token'
    ];

    // protected $with = ['session'];

    protected static array $rolePermissions = [
        Permission::REQUEST_ASSET_ASSIST,
    ];

    protected static function seedPermissions(): array
    {
        return [];
    }

    // For HasChildren
    protected $childColumn = 'type';

    // Important to not use booted method here, it causes issues with the observer.
    public static function boot(): void
    {
        parent::boot();

        static::addGlobalScope('notSystem', function (Builder $builder): void {
            $builder->notSystem();
        });
    }

    /**
     * Get the morph map for the package.
     */
    protected function childTypes(): array
    {
        return [
            'system' => System::class,
            // Roles
            'super_admin' => SuperAdmin::class,
            'admin' => Admin::class,

            'managing_editor' => ManagingEditor::class,
            'editor' => Editor::class,
            'topic_editor' => Editor::class,
            'vertical_editor' => Editor::class,
            'story_editor' => Editor::class,
            'photo_editor' => PhotoEditor::class,
            'copy_editor' => CopyEditor::class,
            'graphics_editor' => GraphicsEditor::class,
            'web_editor' => Editor::class,
            'visual_editor' => Editor::class,

            'reporter' => Reporter::class,
            'editorial_admin' => EditorialAdmin::class,

            'audience_liaison' => AudienceLiaison::class,
            'fact_checker' => FactChecker::class,
            'researcher' => Researcher::class,
            'translator' => Translator::class,
            'interpreter' => Interpreter::class,

            'user' => User::class,
            'photo_archivist' => User::class,
        ];
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'active' => 'boolean',
            'preferences' => 'json',
            'timezone' => Timezone::class,
            'locale' => Location::class,
        ];
    }

    public function canImpersonate(): bool
    {
        return $this->can('impersonate', $this);
    }

    public function canBeImpersonated(): bool
    {
        return $this->can('be_impersonated', $this);
    }

    public function isImpersonated(): bool
    {
        $token = $this->currentAccessToken();

        return $token->name == 'IMPERSONATION token';
    }

    public function impersonations(): HasMany
    {
        return $this->hasMany(Impersonation::class, 'user_id');
    }

    public function deleteImpersonations(): void
    {
        $this->impersonations->each->delete();
    }

    public function hasImpersonations(): bool
    {
        return $this->impersonations()->exists();
    }

    public function impersonated(): ?User
    {
        return $this->impersonations()->first()->user;
    }

    public function hasPitch(Pitch $pitch): bool
    {
        return $this->ownPitches()->where('id', $pitch->getKey())->exists();
    }

    public function like(Model $model, ?string $value = null, array $data = []): void
    {
        $class = 'App\Domains\Betterflow\V1\Pitches\Events\Reactions\Liked' . class_basename($model);
        Like::add(markable: $model, user: $this, value: $value, metadata: $data);
        if (class_exists($class)) {
            event(new $class($this->getKey(), $model->getKey()));
        }
    }

    public function unlike(Model $model): void
    {
        $class = 'App\Domains\Betterflow\V1\Pitches\Events\Reactions\UnLiked' . class_basename($model);
        Like::remove(markable: $model, user: $this);
        if (class_exists($class)) {
            event(new $class($this->getKey(), $model->getKey()));
        }
    }

    public function rollApiKey(string $name = 'lighthouse', array $abilities = ['lighthouse']): NewAccessToken
    {
        $this->deleteApiKeys();

        do {
            $token = $this->createToken($name, $abilities, now()->addHours(24));
            $this->api_token = hash('sha256', $token->plainTextToken);
        } while ($this->where('api_token', hash('sha256', $token->plainTextToken))->exists());

        $this->save();

        return $token;
    }

    public function deleteApiKeys(): void
    {
        $this->tokens()->delete();
        $this->api_token = null;
        $this->save();
    }

    public function ownPitches(): HasMany
    {
        return $this->hasMany(Pitch::class, 'created_by');
    }

    public function watchedPitches(): MorphToMany
    {
        return $this->morphToMany(
            related: Pitch::class,
            // table: 'watchables',
            name: 'watchable',
            relatedPivotKey: 'watchable_id',
            foreignPivotKey: 'user_id',
            // relation: 'pitch',
            relatedKey: 'id',
            // parentKey: 'user_id',
            inverse: true,
        );
    }

    public function collaboratingPitches(): BelongsToMany
    {
        return $this->belongsToMany(Pitch::class, 'pitch_collaborators', 'user_id', 'pitch_id')
            ->notOwnedBy($this->getKey())
            // ->withPivot(['role'])
            ->using(PitchCollaborator::class);
    }

    public function sessions(): HasMany
    {
        return $this->hasMany(PersonalAccessToken::class, 'tokenable_id', 'id');
    }

    public function session(): HasOne
    {
        return $this->hasOne(PersonalAccessToken::class, 'tokenable_id', 'id')->latestOfMany('updated_at');
    }

    public function editorials(): HasMany
    {
        return $this->hasMany(Editorial::class, 'reporter_id');
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class, 'user_id')->whereIn('taskable_type', ['editorial', 'asset', 'pitch']);
    }

    public function rawTasks(): HasMany
    {
        return $this->hasMany(Task::class, 'user_id');
    }

    public function assists(): HasMany
    {
        return $this->tasks()->whereNotNull('requested_by_id')->where('type', 'LIKE', '%_assist');
    }

    public function pendingTasks(): HasMany
    {
        return $this->tasks()->pending()->latest();
    }

    public function completedAssists(): HasMany
    {
        return $this->assists()->completed()->latest();
    }

    public function completedTasks(): HasMany
    {
        return $this->tasks()->notAssists()->completed()->latest();
    }

    public function pendingAssists(): HasMany
    {
        return $this->assists()->pending()->latest();
    }

    public function pitchTasks(): HasMany
    {
        return $this->tasks()->where('taskable_type', 'pitch');
    }

    public function assetTasks(): HasMany
    {
        return $this->tasks()->whereIn('taskable_type', ['asset', 'reporting', 'graphic', 'fact', 'headline_summary', 'illustration', 'photo']);
    }

    public function editorialTasks(): HasMany
    {
        return $this->tasks()->where('taskable_type', 'editorial');
    }

    public function isActive(): bool
    {
        return $this->active ?? false;
    }

    public function isAdmin(): bool
    {
        return $this->hasAnyRole(['admin', 'super_admin']);
    }

    public function isSuperAdmin(): bool
    {
        return $this->hasRole('super_admin');
    }

    public function disabled(): bool
    {
        return ! $this->isActive();
    }

    public function canEdit(Pitch $pitch): bool
    {
        return $this->can('update pitch', $pitch);
    }

    public function photo(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')->where('model_type', str(class_basename($this))->lower())->where('collection_name', 'photos');
    }

    public function enterSpace(Space $space): void
    {
        $this->currentSpace()->associate($space);
        $this->save();
    }

    public function leaveCurrentSpace(): void
    {
        $this->currentSpace()->dissociate();
        $this->save();
    }

    public function pitchEvents()
    {
        $pitchesInMeetings = EventPitch::with(['attendees', 'pitch' => function ($query) {
            $query->where('pitches.created_by', $this->getKey());
        }])
            ->get()->map(function ($pitchEvent) {
                $pitchEvent->event = Event::find($pitchEvent->event_id);
                $pitchEvent->pitch = NotificationPitchResource::make($pitchEvent->pitch);
                $pitchEvent->attendees = SimpleUserResource::collection($pitchEvent->attendees);

                return $pitchEvent;
            });

        return $pitchesInMeetings;
    }

    public function currentSpace(): BelongsTo
    {
        return $this->belongsTo(Space::class, 'current_space_id');
    }

    public function updatePhoto(UploadedFile $photo): string
    {

        if ($this->photo_url && Storage::disk(config('filesystems.cloud'))->exists($this->photo_url)) {
            Storage::disk(config('filesystems.cloud'))->delete($this->photo_url);
        }

        $path = $photo->store('users/' . $this->getKey(), config('filesystems.cloud'));
        $this->photo_url = $path;
        $this->save();

        return $this->getPhotoUrl();
    }

    public function getPhotoUrl(): string
    {

        //    Retrtieve photo from cache or new
        return Cache::remember(sprintf('user:photo:%s:%s', $this->getKey(), $this->photo_url), 60, function () {
            if (! $this->photo_url) {
                return '';
            }
    
            if (str($this->photo_url)->contains('http')) {
                return $this->photo_url;
            }
    
            $path = Storage::disk(config('filesystems.cloud'))->path($this->photo_url);
    
            return Storage::disk(config('filesystems.cloud'))->url($path);
        });
    }

    public function getTwoFactorEnabledAttribute(): bool
    {
        return ! empty($this->two_factor_secret) && ! empty($this->two_factor_recovery_codes);
    }

    public function getRecoveryCodesAttribute(): array|string|null
    {
        return $this->two_factor_enabled ? json_decode(decrypt(
            $this->two_factor_recovery_codes,
        )) : null;
    }

    public function getQrCodeAttribute(): ?array
    {
        return $this->two_factor_enabled ? [
            'svg' => $this->twoFactorQrCodeSvg(),
            'url' => $this->twoFactorQrCodeUrl(),
        ] : null;
    }

    /**
     * Get the user's timezone readable format.
     */
    protected function timezoneReadable(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->timezone,
        );
    }

    public function deactivate(): void
    {
        $this->active = false;
        $this->save();
    }

    /**
     * Delete the user and all related data.
     *
     * return bool|null
     * @throws \Exception
     */
    public function delete(): bool|null
    {
        DB::beginTransaction();
        $this->ownPitches()->delete();
        DB::commit();
        DB::afterCommit(function () {
            parent::delete();
        });

        return true;
    }

    public function frontendUserUrl(): string
    {
        return config('app.frontend_url') . '/settings/users';
    }

    public function notificationsFor(string $origin): Builder
    {
        return $this->notifications()->whereJsonContains('data->from_where', $origin);
    }

    public static function getRoleName(): string
    {
        return str(basename(str_replace('\\', '/', get_called_class())))->snake()->lower()->toString();
    }

    public static function getMappedClass(): string
    {
        $propertyName = static::getRoleName();

        return data_get(Relation::morphMap(), $propertyName);
    }

    public static function getModelName(): string
    {
        return basename(static::class);
    }

    public static function getRolePermissions(): array
    {
        $basePermissions = get_class_vars(get_parent_class(static::class))['rolePermissions'] ?? [];
        $childPermissions = static::getModelName()::seedPermissions() ?? [];

        $mergedPermissions = collect(array_merge($basePermissions, $childPermissions))->unique()->toArray();

        return $mergedPermissions;
    }

    public function scopeWhereRoleNames(Builder $query, ...$roleNames): Builder
    {
        return $query->whereHas('roles', function (Builder $query) use ($roleNames): void {
            if (! empty($roleNames)) {
                $roleNames = [$roleNames];
            }

            $query->whereIn('name', $roleNames);
        });
    }

    public function scopeNotSystem(Builder $query): Builder
    {
        return $query->whereNot('type', 'system');
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->whereActive(true)->whereNull('deleted_at');
    }

    public function scopeVerified(Builder $query): Builder
    {
        return $query->whereNotNull('email_verified_at');
    }

    protected static function newFactory()
    {
        $class = str(basename(str_replace('\\', '/', static::class))) . 'Factory';

        $classPath = str(static::class)->before('\\Models')->append('\\Database\\Factories\\')->toString();

        if (class_exists($classPath . $class, false)) {
            $factory = ($classPath . $class)::new();
        } else {
            $factory = UserFactory::new();
        }

        return $factory->withRole(static::class)->setModel(static::class);
    }

    public function receivesBroadcastNotificationsOn(Notification $notification): string
    {
        return 'User.Notifications.' . $this->getKey();
    }

    public static function getUserCountDiagnostics()
    {
        $totalUsers = self::withoutGlobalScopes()->count();
        $activeUsers = self::active()->count();
        $verifiedUsers = self::verified()->count();
        $nonSystemUsers = self::notSystem()->count();
        $activeVerifiedNonSystemUsers = self::active()->verified()->notSystem()->count();

        logger('User Count Diagnostics', [
            'Total Users' => $totalUsers,
            'Active Users' => $activeUsers,
            'Verified Users' => $verifiedUsers,
            'Non-System Users' => $nonSystemUsers,
            'Active, Verified, Non-System Users' => $activeVerifiedNonSystemUsers,
        ]);

        return [
            'total' => $totalUsers,
            'active' => $activeUsers,
            'verified' => $verifiedUsers,
            'nonSystem' => $nonSystemUsers,
            'activeVerifiedNonSystem' => $activeVerifiedNonSystemUsers,
        ];
    }

    public static function listGlobalScopes()
    {
        $model = new static;
        $reflection = new \ReflectionClass($model);

        // Find the method that applies global scopes
        try {
            $method = $reflection->getMethod('registerGlobalScopes');
            $method->setAccessible(true);

            // Create a mock query builder to capture scopes
            $queryBuilder = \Mockery::mock(\Illuminate\Database\Eloquent\Builder::class);
            $scopeNames = [];

            $queryBuilder->shouldReceive('withGlobalScope')
                ->andReturnUsing(function ($name, $scope) use (&$scopeNames, $queryBuilder) {
                    $scopeNames[] = $name;

                    return $queryBuilder;
                });

            // Apply global scopes
            $method->invoke($model, $queryBuilder);

            // Log and return the scopes
            logger('Global Scopes for ' . get_class($model), [
                'scopes' => $scopeNames,
            ]);

            return $scopeNames;
        } catch (\ReflectionException $e) {
            logger('Error finding global scopes', [
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }
}
