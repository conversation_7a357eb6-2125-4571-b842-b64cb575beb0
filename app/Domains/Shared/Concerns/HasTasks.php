<?php

namespace App\Domains\Shared\Concerns;

use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task as DataTask;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskAdded;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Assist;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HasTasks
{
    // Owner Tasks / General Tasks
    public function tasks(): MorphMany
    {
        return $this->morphMany(Task::class, 'taskable')->chaperone('taskable');
    }

    public function rawTasks(): MorphMany
    {
        return $this->morphMany(Task::class, 'taskable');
    }

    public function pendingTasks(): MorphMany
    {
        return $this->tasks()->pending()->latest();
    }

    public function completedTasks(): MorphMany
    {
        return $this->tasks()->completed()->latest();
    }

    public function pendingTasksForUser(string $userId): Collection
    {
        return $this->pendingTasks()
            ->where('user_id', $userId)
            ->get();
    }

    public function tasksOnHold(): MorphMany
    {
        return $this->tasks()->onHold()->latest();
    }

    public function hasTasks(): bool
    {
        return $this->rawTasks()->exists();
    }

    public function hasPendingTasks(): bool
    {
        return $this->pendingTasks()->exists();
    }

    public function ownerTask(): Task|Model|null
    {
        return $this->tasks()->where('user_id', $this->loadMissing('assignee')->assignee?->getKey())->whereNull('requested_by_id')->first();
    }

    public function isOwnerTask(Task $task): bool
    {
        $task->loadMissing('taskable.assignee', 'user');

        return is_null($task->requested_by_id) && $task->taskable->assignee?->is($this->user);
    }

    public function createTask(DataTask $task, bool $notify = true): void
    {
        $newTask = new Task();
        $newTask->fill($task->toArray());

        $newTask->requested_by_id = $task->requestedById;
        $this->rawTasks()->save($newTask);

        // Automatically send notification when task created
        if ($notify) {
            TaskAdded::dispatch($newTask);
        }
    }

    public function addTask(DataTask $taskData): Task
    {
        $task = new Task();
        $task->fill($taskData->toArray());
        $this->rawTasks->push($task);

        return $task;
    }

    // Assists
    public function assists(): MorphMany
    {
        return $this->morphMany(Assist::class, 'taskable')->chaperone('taskable');
    }

    public function rawAssists(): MorphMany
    {
        return $this->morphMany(Assist::class, 'taskable')->latest();
    }

    public function pendingAssists(): MorphMany
    {
        return $this->assists()->pending()->latest();
    }

    public function pendingAssistsForUser(string $userId): Collection
    {
        return $this->pendingAssists()
            ->where('user_id', $userId)
            ->get();
    }

    public static function getSubjectName(): string
    {
        return str(basename(str_replace('\\', '/', static::class)))->snake()->lower();
    }

    public function addAssist(DataTask $taskData): Assist
    {
        $task = new Assist();
        $task->fill($taskData->toArray());
        $this->rawAssists->push($task);

        return $task;
    }

    public function createAssist(DataTask $task): void
    {
        $newTask = new Assist();
        $newTask->fill($task->toArray());

        $newTask->requested_by_id = $task->requestedById;
        $this->rawAssists()->save($newTask);

        // Automatically send notification when task created
        TaskAdded::dispatch($newTask);
    }

    public function hasAssists(): bool
    {
        return $this->rawAssists()->exists();
    }

    public function hasPendingAssists(): bool
    {
        return $this->pendingAssists()->exists();
    }
}
