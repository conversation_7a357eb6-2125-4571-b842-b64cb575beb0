<?php

namespace App\Domains\Betterflow\V1\Editorials\Jobs;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task as DataTask;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskAdded;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Throwable;

class CheckForCompletedAssets implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    public function __construct(
        #[WithoutRelations]
        protected Asset $asset,
        protected Task $task,
    ) {}

    public function handle(): void
    {
        /** @var Task $task */
        $task = $this->task;

        if ($task->isOwnerTask() && $task->taskable instanceof Asset) {
            $taskable = $task->taskable;

            $editorial = $taskable->editorial->refresh();

            if ($editorial->allAssetsComplete()) {
                try {
                    DB::beginTransaction();
                    if ($topLineEditor = $editorial->toplineEditor) {
                        $newTask = new DataTask(
                            userId: $topLineEditor->getKey(),
                            title: 'Approve topline edit and send to production',
                            description: 'Approve topline edit and send to production',
                            type: EditorialTaskType::ToplineEdit,
                            dueAt: now()->addDays(2),
                            data: new TaskData(
                                model: $editorial,
                                label: 'Approve topline edit and send to production',
                            ),
                        );

                        $task = $editorial->addTask($newTask);

                        $editorial->recordEvent(new TaskAdded($task));

                        $editorial->moveSubPhase(EditorialSubPhase::ToplineEdit);

                        $editorial->persist();
                        DB::commit();
                    }
                } catch (\Throwable $e) {
                    DB::rollBack();
                    // $this->fail($e);
                    throw $e;
                }
            }
        }
    }
}
