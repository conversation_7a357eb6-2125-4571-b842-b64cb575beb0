<?php

namespace App\Domains\Betterflow\V1\Editorials\Notifications;

use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\TaskType;
use App\Domains\Shared\Data\NotificationAction;
use App\Domains\Shared\Data\NotificationDTO;
use App\Domains\Shared\Enums\MessageType;
use Illuminate\Bus\Queueable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DefaultNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly ?Model $forWhat,
        public readonly string $title,
        public readonly string $message,
        public readonly string|TaskType|null $type = null,
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage())
            ->line('The introduction to the notification.')
            ->action('View Details', url('/'))
            ->greeting('Thank you!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return (new NotificationDTO(
            user: $notifiable,
            forWhat: $this->forWhat ?? null,
            type: $this->type ?? null,
            title: $this->title ?? 'Default Notification',
            message: $this->message ?? 'Default Notification',
            origin: 'betterflow',
            action: new NotificationAction(
                severity: MessageType::Muted,
                buttonLabel: 'View Details',
                message: 'View',
            ),
        ))->toArray();
    }

    public function databaseType(object $notifiable): string
    {
        if(!$this->type) return 'default';

        if ($this->type instanceof TaskType) {
            return $this->type->value;
        }

        return $this->type ?? 'default_notification';
    }
}
