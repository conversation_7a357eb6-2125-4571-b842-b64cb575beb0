<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\Production;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;


class CreateTaskForFactCheckerWhenAssigned implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    public static function handle(EditorialMovedSubPhase $event): void
    {
        $editorial = Editorial::find($event->editorialId);

        if ($editorial->isInProduction() && $editorial->sub_phase === EditorialSubPhase::FactCheck) {
            $task = new Task(
                userId: $editorial->factChecker->getKey(),
                title: "Fact check and send to Copy Edit",
                description: "You are assigned to fact check this editorial and send it to Copy Edit when complete",
                type: EditorialTaskType::FactCheck,
                data: new TaskData(
                    label: '',
                    model: $editorial,
                ),
            );

            $editorial->createTask($task);
        }
    }
}
