<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Enums\Assets\RecordableEvents;
use App\Domains\Betterflow\V1\Editorials\Events\TaskCompleted;
use App\Domains\Betterflow\V1\Editorials\Jobs\CheckForCompletedAssets;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted as SharedEventsTaskCompleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class RecordActivityAndNotifyRequester implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    public function handle(TaskCompleted|SharedEventsTaskCompleted $event): void
    {
        $task = $event->task;

        if ($task->taskable instanceof Asset && $task->isOwnerTask()) {
            $asset = $task->taskable;

            $asset->markComplete();

            CheckForCompletedAssets::dispatch($asset, $task);
        }

        // $activity = new ActivityObject(
        //     logName: str($task->taskable_type)->plural()->toString(),
        //     on: $task->taskable,
        //     by: (string) $task->user_id,
        //     event: RecordableEvents::AssistCompleted,
        //     description: 'Completed Assist',
        //     properties: [],
        // );

        // RecordActivity::dispatch($activity);

        // Handles assists
        if ($task->isAssist()) {
            $task->requestedBy->notify(new DefaultNotification(
                forWhat: $task->taskable,
                title: $task->title,
                message: 'An assist you requested has been marked complete',
                type: $task->type . '_completed',
            ));
        }
    }
}
