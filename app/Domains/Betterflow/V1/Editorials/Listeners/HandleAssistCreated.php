<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Enums\Assets\RecordableEvents;
use App\Domains\Betterflow\V1\Editorials\Events\AssistCreated;
use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Queue\InteractsWithQueue;
use Throwable;

class HandleAssistCreated implements ShouldQueueAfterCommit
{
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public function handle(AssistCreated $event): void
    {
        /** @var Task */
        $task = $event->task;

        $notificationType = data_get($task->data, 'properties.notification_type', $task->type);

        $task->user->notify(new DefaultNotification(
            forWhat: $task->taskable,
            title: 'Your assistance is required on ' . $task->taskable?->getName(),
            message: 'You have been assigned a new assist',
            type: $notificationType,
        ));

        $activity = new ActivityObject(
            logName: str($task->taskable_type)->plural()->toString(),
            by: $task->requestedBy?->getKey(),
            on: $task->taskable,
            event: RecordableEvents::AssistRequested,
            description: 'Requested an assist',
            properties: [
                'notification_type' => 'requested_assist'
            ],
        );

        RecordActivity::dispatch($activity);
    }

    public function failed(AssistCreated $event, Throwable $exception): void
    {
        logger('HandleAssistCreated listener failed', [
            'exception' => $exception->getMessage(),
            // 'task_id' => $this->event->assist->id ?? null,
            // 'task_type' => get_class($this->event->assist) ?? null
        ]);
    }
}
