<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Events\AssetCreated;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Throwable;

class IssueAssetTask implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    /**
     * Handle the event.
     */
    public function handle(AssetCreated $event): void
    {
        /** @var Asset $asset */
        $asset = $event->asset;
        $assignee = $asset->assignee;

        $assetType = $asset->type->plural();

        // Create Task
        $task = new Task(
            userId: $assignee->getKey(),
            title: sprintf('Prepare %s for topline edit', $assetType),
            description: 'A new asset has been created and needs topline editing',
            type: sprintf('prepare_%s_topline_edit', $assetType),
            dueAt: now()->addDays(10),
            data: new TaskData(
                model: $asset,
                label: sprintf('Prepare %s for topline edit', $assetType),
                properties: [
                    'notification_type' => 'prepare_asset_for_topline_edit',
                ],
            ),
        );

        $asset->createTask($task);
    }

    public function failed(Throwable $exception): void
    {
        \Log::error('IssueAssetTaskNotification listener failed', [
            'exception' => $exception->getMessage(),
            // 'asset_id' => $this->event->asset->id ?? null,
            // 'asset_type' => get_class($this->event->asset) ?? null
        ]);
    }
}
