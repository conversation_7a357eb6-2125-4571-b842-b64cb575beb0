<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Jobs\CreateAssist;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use Illuminate\Auth\Access\Response;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Gate;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RequestAssetAssist
{
    use AsAction;

    public function authorize(ActionRequest $request, #[CurrentUser] $user, #[RouteParameter('asset')] Asset $asset): Response
    {
        // FIXME: authorize users correctly!
        return Gate::authorize('request_assist', $asset);
    }

    public function rules(): array
    {
        return [
            'tab' => ['sometimes', 'string'],
            'type' => ['sometimes', 'required_without:tab', 'string'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['sometimes', 'string', 'max:3000'],
            'due_at' => ['sometimes', 'date', 'after:today'],
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'instructions' => ['sometimes', 'string', 'max:3000'],
        ];
    }

    public function messages(): array
    {
        return [
            'type.required_without' => 'The type is required if tab is missing.',
        ];
    }

    public function handle(Asset $asset, Task $task): void
    {
        CreateAssist::dispatch($asset, $task);
    }

    public function asController(ActionRequest $request, Editorial $editorial, Asset $asset): JsonResponse
    {
        $this->handle($asset, $this->taskFromRequest($asset, $request));

        return response()->json([
            'success' => true,
            'message' => 'Assistance requested.',
        ]);
    }

    public function taskFromRequest(Asset $asset, ActionRequest $request): Task
    {
        $data = $request->validated();

        $type = data_get($data, 'type');
        $type = $type ? $type : sprintf('%s_%s_assist', $asset->slug, data_get($data, 'tab'));

        return new Task(
            userId: data_get($data, 'user_id'),
            requestedById: $request->user()->getKey() ?? null,
            title: data_get($data, 'title'),
            description: data_get($data, 'description'),
            type: $type,
            dueAt: Date::createFromDate(data_get($data, 'due_at', null)),
            data: new TaskData(
                model: $asset,
                label: data_get($data, 'title'),
                properties: [
                    'instructions' => data_get($data, 'instructions'),
                    'notification_type' => 'requested_assist'
                ],
            ),
        );
    }
}
