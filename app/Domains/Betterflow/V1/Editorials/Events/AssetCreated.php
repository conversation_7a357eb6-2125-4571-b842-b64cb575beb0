<?php

namespace App\Domains\Betterflow\V1\Editorials\Events;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AssetCreated implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly Asset $asset,
    ) {
        //
    }
}
