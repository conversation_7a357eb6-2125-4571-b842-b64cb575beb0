<?php

namespace App\Domains\Betterflow\V1\Editorials\Enums;

use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Shared\Concerns\EnumHelpers;
use Illuminate\Support\Collection;

enum EditorialSubPhase: string
{
    use EnumHelpers;

        // Editing
    case EditingAssignment = 'editing_assignment';
    case Editing = 'editing';
    case ToplineEdit = 'topline_edit';

        // Production
    case FactCheckAssignment = 'fact_check_assignment';
    case FactCheck = 'fact_check';
    case CopyEdit = 'copy_edit';
    case TranslationAssignment = 'translation_assignment';
    case Translation = 'translation';

        // Publishing
    case PublishingAssignment = 'publishing_assignment';
    case Load = 'load';
    case FinalReview = 'final_review';
    case Scheduling = 'scheduling';

    public static function stagesForPhase(EditorialPhase $phase): Collection
    {
        $stages = match ($phase) {
            EditorialPhase::Editing => [
                self::EditingAssignment,
                self::Editing,
                self::ToplineEdit,
            ],
            EditorialPhase::Production => [
                self::FactCheckAssignment,
                self::FactCheck,
                self::CopyEdit,
                self::TranslationAssignment,
                self::Translation,
                self::FinalReview,
            ],
            EditorialPhase::Publishing => [
                self::PublishingAssignment,
                self::Load,
                self::FinalReview,
                self::Scheduling,
            ],
            EditorialPhase::Published => [],
            default => []
        };

        return collect($stages);
    }

    public function label(): string
    {
        return match ($this) {
            self::EditingAssignment => 'Editing Assignment',
            self::Editing => 'Editing',
            self::ToplineEdit => 'Topline Edit',
            self::FactCheckAssignment => 'Fact Check Assignment',
            self::FactCheck => 'Fact Check',
            self::CopyEdit => 'Copy Edit',
            self::TranslationAssignment => 'Translation Assignment',
            self::Translation => 'Translation',
            self::PublishingAssignment => 'Publishing Assignment',
            self::Load => 'Load',
            self::FinalReview => 'Final Review',
            self::Scheduling => 'Scheduling',
        };
    }

    public function relation(): string
    {
        return match ($this) {
            self::FactCheck => 'factChecker',
            self::CopyEdit => 'copyEditor',
            self::Translation => 'translator',
            default => 'assignee',
        };
    }

    public function taskType(): EditorialTaskType
    {
        return match($this) {
            self::EditingAssignment => EditorialTaskType::AssignEditors,
            self::Editing => EditorialTaskType::Plan,
            self::ToplineEdit => EditorialTaskType::ToplineEdit,
            self::FactCheckAssignment => EditorialTaskType::AssignFactCheckerAndCopyEditor,
            self::FactCheck => EditorialTaskType::FactCheck,
            self::CopyEdit => EditorialTaskType::CopyEdit,
            self::TranslationAssignment => EditorialTaskType::AssignTranslator,
            self::Translation => EditorialTaskType::Translation,
            default => EditorialTaskType::Plan,
        };
    }
}
