<?php

namespace App\Domains\Betterflow\V1\Editorials\Models;

use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\TaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Shared\Concerns\HasComments;
use App\Domains\Shared\Concerns\HasTasks;
use App\Domains\Shared\Contracts\HasName;
use App\Domains\Shared\Contracts\HasPublicKey as HasPublicKeyContract;
use App\Domains\Shared\Contracts\HasState as ContractsHasState;
use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Shared\Models\Concerns\HasPublicKey;
use App\Domains\Users\Models\User;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use GPJ\Watchable\Traits\Watchable;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Mpociot\Versionable\VersionableTrait;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Editorial extends BaseDomainModel implements ContractsHasState, HasPublicKeyContract, HasName
{
    use CascadeSoftDeletes;
    use HasComments;
    use HasPublicKey;
    use HasTasks;
    use LogsActivity;
    use SoftDeletes;
    use VersionableTrait;
    use Watchable;

    protected $guarded = ['id', 'public_id', 'reporter_id', 'assigned_to_id', 'topline_editor_id', 'copy_editor_id', 'fact_checker_id', 'translator_id'];

    protected $cascadeDeletes = ['assets', 'assignments', 'pitch'];

    protected $casts = [
        'phase' => EditorialPhase::class,
        'sub_phase' => EditorialSubPhase::class,
        'deleted_at' => 'datetime',
    ];

    protected $with = [];

    public function getName(): string
    {
        return $this->getAttribute('name') ?? '';
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('editorials')
            ->logUnguarded()
            ->dontSubmitEmptyLogs();
    }

    public function currentStages(): array|Collection
    {
        return EditorialSubPhase::stagesForPhase($this->phase);
    }

    public function setInitialStages(): Editorial
    {
        $this->phase = EditorialPhase::Editing;
        $this->sub_phase = EditorialSubPhase::EditingAssignment;

        return $this;
    }

    public function createInitialStage(): Editorial
    {
        $this->phase = EditorialPhase::Editing;
        $this->sub_phase = EditorialSubPhase::EditingAssignment;

        $this->save();

        return $this;
    }

    private function autoCompleteTasksEditing(): array
    {
        return match ($this->sub_phase) {
            EditorialSubPhase::EditingAssignment => [
                EditorialTaskType::AssignEditors,
            ],
            default => []
        };
    }

    private function autoCompleteTasksProduction(): array
    {
        return match ($this->sub_phase) {
            EditorialSubPhase::FactCheckAssignment => [
                EditorialTaskType::AssignFactCheckerAndCopyEditor,
            ],
            EditorialSubPhase::TranslationAssignment => [
                EditorialTaskType::AssignTranslator,
            ],
            default => []
        };
    }

    public function autoCompleteTasks(): array
    {
        $phase = $this->phase;

        $taskTypes = match ($phase) {
            EditorialPhase::Editing => $this->autoCompleteTasksEditing(),
            EditorialPhase::Production => $this->autoCompleteTasksProduction(),
            default => [],
        };

        // Convert enum tasks to their string values
        return array_map(function ($type) {
            return $type instanceof TaskType ? $type->value : $type;
        }, $taskTypes);
    }

    public function initialSubphaseFor(EditorialPhase $phase): ?EditorialSubPhase
    {
        $subPhases = EditorialSubPhase::stagesForPhase($phase);

        return $subPhases->first();
    }

    public function movePhase(EditorialPhase $phase, ?EditorialSubPhase $subPhase = null): void
    {
        $dataToUpdate = [
            'phase' => $phase,
        ];

        if ($subPhase !== null) {
            $dataToUpdate['sub_phase'] = $subPhase;
        }

        $this->fill($dataToUpdate);
    }

    public function moveSubPhase(EditorialSubPhase $subPhase): void
    {
        $this->sub_phase = $subPhase;
    }

    public function isInEditing(): bool
    {
        return $this->phase === EditorialPhase::Editing;
    }

    public function isInProduction(): bool
    {
        return $this->phase === EditorialPhase::Production;
    }

    public function isInPublishing(): bool
    {
        return $this->phase === EditorialPhase::Publishing;
    }

    public function isInPublished(): bool
    {
        return $this->phase === EditorialPhase::Published;
    }

    public function reassignTasks(User $newAssignee, User $previousAssignee): void
    {
        $currentTasks = $previousAssignee->pendingTasks()->where('taskable_id', $this->getKey())->where('taskable_type', $this->getMorphClass())->get();

        foreach ($currentTasks as $task) {
            $task->user_id = $newAssignee->getKey();
            $task->due_at = now()->addDays(2);
            $task->saveQuietly();
        }
    }

    public function getRouteKeyName(): string
    {
        $length = str(request()->route('editorial'))->length();
        $uuid_length = 36;

        return $length == $uuid_length ? $this->getPublicKeyName() : $this->getKeyName();
    }

    public function packages(): HasMany
    {
        return $this->hasMany(EditorialPackage::class, 'editorial_id');
    }

    public function primaryPackage(): HasOne
    {
        return $this->hasOne(EditorialPackage::class, 'editorial_id')->take(1)->where('language_code', $this->default_language);
    }

    public function packageForLanguage(string $languageCode): ?EditorialPackage
    {
        return $this->packages()->where('language_code', $languageCode)->first();
    }

    public function reporter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reporter_id');
    }

    public function assets(): HasMany
    {
        return $this->hasMany(Asset::class);
    }

    public function assetOfType(AssetType $type): ?Asset
    {
        /* @phpstan-ignore-next-line */
        return $this->assets()->whereType($type)->first();
    }

    public function addAsset(Asset $asset): Editorial
    {
        $this->assets->push($asset);

        return $this;
    }

    public function createAsset(Asset $asset): Editorial
    {
        $this->assets->create($asset);

        return $this;
    }

    public function allAssetsComplete(): bool
    {
        $assets = $this->assets;

        $completedAssetsCount = $assets->whereNotNull('completed_at')->count();

        return $assets->count() === $completedAssetsCount;
    }

    public function addPackage(string $languageCode, array $data = []): Editorial
    {
        $this->packages->push(new EditorialPackage([
            'language_code' => $languageCode,
            'data' => $data
        ]));

        return $this;
    }

    public function createPackage(string $languageCode, array $data = []): Editorial
    {
        $this->packages()->create([
            'language_code' => $languageCode,
            'data' => $data
        ]);

        return $this;
    }

    public function pitch(): BelongsTo
    {
        return $this->belongsTo(Pitch::class);
    }

    public function assignments(): MorphMany
    {
        return $this->morphMany(Assignment::class, 'assignable');
    }

    public function completedAssets(): HasMany
    {
        return $this->assets()->complete();
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_id');
    }

    public function toplineEditor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'topline_editor_id');
    }

    public function copyEditor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'copy_editor_id');
    }

    public function factChecker(): BelongsTo
    {
        return $this->belongsTo(User::class, 'fact_checker_id');
    }

    private function convertRoletoRelation(string $role): string
    {
        return str($role)->lower()->studly()->lcfirst()->toString();
    }

    public function translator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'translator_id');
    }

    public function getRelationForKey(string $key): ?Model
    {
        return match ($key) {
            'assigned_to_id' => $this->assignee,
            'assignee' => $this->assignee,
            'topline_editor_id' => $this->toplineEditor,
            'topline_editor' => $this->toplineEditor,
            'copy_editor_id' => $this->copyEditor,
            'copy_editor' => $this->copyEditor,
            'fact_checker_id' => $this->factChecker,
            'fact_checker' => $this->factChecker,
            'translator_id' => $this->translator,
            'translator' => $this->translator,
            'reporter_id' => $this->reporter,
            'reporter' => $this->reporter,
            'pitch_id' => $this->pitch,
            default => null
        };
    }

    /* @phpstan-ignore return.type */
    public function currentOwner(): ?User
    {
        $assignee = $this->relationLoaded('assignee') ? $this->assignee : $this->loadMissing('assignee');

        /** @var EditorialSubPhase $subPhase */
        $subPhase = $this->sub_phase;

        /* @phpstan-ignore-next-line */
        if (! $this->currentStages()->contains($subPhase)) {
            /* @phpstan-ignore return.type */
            return $assignee;
        }

        $relation = $subPhase->relation();

        /* @phpstan-ignore-next-line */
        if ($this->isRelation($relation)) {
            return $this->$relation;
        }

        /* @phpstan-ignore return.type */
        return $assignee;
    }

    public function hasAssignedUser(User $user, ?string $role = null): bool
    {
        if (!is_null($role)) {
            return $this->getRelationForKey($role)?->is($user) ?? false;
        }

        if ($this->assignee?->is($user)) {
            return true;
        }

        if ($this->toplineEditor?->is($user)) {
            return true;
        }

        if ($this->copyEditor?->is($user)) {
            return true;
        }

        if ($this->reporter?->is($user)) {
            return true;
        }

        if ($this->factChecker?->is($user)) {
            return true;
        }

        if ($this->translator?->is($user)) {
            return true;
        }

        return false;
    }

    public function currentTask(): MorphOne
    {
        return $this->tasksForPhase()->one();
    }

    public function ownTasks(): MorphMany
    {
        return $this->tasks()->notAssists()->latest();
    }

    public function tasksForPhase(): MorphMany
    {
        return $this->pendingTasks()->where('type', $this->sub_phase?->taskType());
    }

    public function needsTranslation(): bool
    {
        $this->loadMissing('packages');

        return $this->packages->count() > 1;
    }

    public function assignUsers(Collection|array $users): Editorial
    {
        collect($users)->each(function (User $user): void {
            $this->assignUser($user, $user->roles->first()->name);
        });

        return $this;
    }

    public function assignUser(User|string $user, ?string $role = null): Editorial
    {
        if (! $user instanceof User) {
            $user = User::query()->find($user);
        }

        $relation = str($role)->lower()->studly()->lcfirst()->toString();

        // if($relation === 'reporter' && $this->reporter?->isNot($user)) {
        //     return $this;
        // }

        if ($relation && $this->isRelation($relation)) {
            $this->$relation()->associate($user);
        } else {
            $this->assignments()->create([
                'user_id' => $user->getKey(),
                'role' => $role ?? $user->type,
            ]);
        }

        return $this;
    }

    public function hasAssetsOfType(string $type): bool
    {
        return $this->assets()->whereType($type)->exists();
    }
}
