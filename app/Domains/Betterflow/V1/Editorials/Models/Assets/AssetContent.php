<?php

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets;

use App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory;
use App\Domains\Shared\Models\Activity;
use App\Domains\Shared\Models\BaseDomainModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Mpociot\Versionable\VersionableTrait;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * AssetContent model representing detailed content for editorial assets.
 */
class AssetContent extends BaseDomainModel
{
    use LogsActivity;
    use SoftDeletes;
    use VersionableTrait;

    protected $table = 'asset_contents';

    protected $guarded = [];

    protected $casts = [
        'data' => 'json',
        'deleted_at' => 'datetime',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('assets')
            ->logUnguarded();
    }

    public function tapActivity(Activity $activity, string $eventName): void
    {
        if (in_array($eventName, ['created', 'updated'])) {
            $activity->event = str($eventName)->headline()->prepend('AssetContent')->toString();
        }

        $activity->subject_id = $this->asset_id;
        $activity->subject_type = 'asset';
    }

    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'asset_id');
    }

    protected static function newFactory()
    {
        return AssetFactory::new();
    }
}
