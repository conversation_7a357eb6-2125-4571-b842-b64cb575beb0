<?php

namespace App\Domains\Betterflow\V1\Editorials\Models;

use App\Domains\Betterflow\V1\Editorials\Database\Factories\EditorialPackageFactory;
use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Shared\Models\Concerns\HasPublicKey;
use App\Domains\Shared\Contracts\HasPublicKey as HasPublicKeyContract;
use App\Domains\Shared\Models\Concerns\HasResourceKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\FileAdder;
use Illuminate\Support\Str;

class EditorialPackage extends BaseDomainModel implements HasPublicKeyContract, HasMedia
{
    use <PERSON><PERSON><PERSON><PERSON><PERSON>ey;
    use HasResourceKey;
    use HasFactory;
    use InteractsWithMedia;

    protected $table = 'editorial_packages';

    protected $guarded = ['id', 'public_id', 'editorial_id'];

    protected $casts = [
        'deleted_at' => 'datetime',
        'data' => 'json',
    ];

    protected $with = [];

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return EditorialPackageFactory::new();
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        if (request()->has('channel_name')) {
            return 'public_id';
        }
        $package = request()->route()?->parameter('package');
        if ($package && Str::isUuid($package)) {
            return 'public_id';
        }
        return 'id';
    }

    /**
     * @return MorphMany<TMedia, $this>
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany($this->getMediaModel(), 'model');
    }

    public function hasAttachments(): bool
    {
        return $this->attachments()->exists();
    }

    public function getLanguageCode(): string
    {
        return $this->getAttribute('language_code');
    }

    public function editorial(): BelongsTo
    {
        return $this->belongsTo(Editorial::class);
    }

    public function addAllAttachmentsFromRequest(Request $request, string $collection): Collection
    {
        $uploadedAttachments = $request->file('attachments');
        $fileAdderCollection = collect($this->addMultipleMediaFromRequest(['attachments']));
        $addedAttachmentIds = collect();

        $fileAdderCollection->each(function (FileAdder $fileAdder, int $index) use ($uploadedAttachments, $request, $addedAttachmentIds, $collection): void {
            $uploadedAttachment = $uploadedAttachments[$index];
            $attachmentExtension = $uploadedAttachment->getClientOriginalExtension();

            $addedAttachment = $fileAdder
                ->usingName($uploadedAttachment->getClientOriginalName())
                ->usingFileName(Str::uuid()->toString() . '.' . $attachmentExtension)
                ->withResponsiveImages()
                ->preservingOriginal()
                ->withCustomProperties([
                    'uploaded_by' => $request->user()->getKey(),
                ])
                ->toMediaCollection($collection, config('globalpress.preferred__media_disk'));

            // AttachmentAdded::dispatch($this->getKey(), $addedAttachment);

            $addedAttachmentIds->add($addedAttachment->getKey());
        });

        // AttachmentsAdded::dispatch($this->getKey(), $request->user(), $addedAttachmentIds->toArray());

        return $addedAttachmentIds;
    }
}
