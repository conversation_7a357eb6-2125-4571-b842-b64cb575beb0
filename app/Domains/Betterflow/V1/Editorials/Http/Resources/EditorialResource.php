<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Shared\Helpers\LanguageHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EditorialResource extends JsonResource
{
    public static $wrap;

    public function toArray(Request $request): array
    {
        $isSingleEditorial = $request->route()?->parameter('editorial') ?? false;

        $country = LanguageHelper::country($this->pitch->country_code);

        $additional = $this->additional;

        if ($this->isInPublishing() && $isSingleEditorial) {
            $packages = $this->loadMissing('packages')->packages;

            $additional['packages'] = $packages;
        }

        if ($isSingleEditorial) {
            $tasks =  TaskResource::collection($this->whenLoaded('ownTasks')->groupBy('status')->map) ?? [];
            $assists = TaskResource::collection($this->whenLoaded('assists')->groupBy('status')->map)  ?? [];

            $additional['tasks'] = $tasks->additional(['editorial_id' => $this->getResourceKey()]);
            $additional['assists'] = $assists->additional(['editorial_id' => $this->getResourceKey()]);
        }

        $data = [
            'id' => $this->getResourceKey(),
            'name' => $this->name,
            'pitch_type' => $this->pitch?->type,
            'current_owner' => SimpleUserResource::make($this->currentOwner()),
            'assignee' => SimpleUserResource::make($this->whenLoaded('assignee')),
            'comments' => CommentsResource::collection($this->whenLoaded('comments')),
            'phase' => $this->phase->value,
            'phase_formatted' => $this->phase->headline(),
            'sub_phase' => $this->when($this->sub_phase, fn() => $this->sub_phase?->value, null),
            'sub_phase_formatted' => $this->when($this->sub_phase, fn() => $this->sub_phase?->headline(), ''),
            'topic' => $this->pitch?->topic,
            'short_name' => $this->pitch?->short_name,
            'vertical' => $this->pitch?->vertical,
            'current_task' => TaskResource::make($this->ownTasks->first())->additional(['editorial_id' => $this->getResourceKey()]),
            'needs_translation' => $this->resource->needsTranslation(),
            'reporter' => SimpleUserResource::make($this->whenLoaded('reporter')),
            'assigned_editor' => SimpleUserResource::make($this->whenLoaded('assignee')),
            'topline_editor' => SimpleUserResource::make($this->whenLoaded('toplineEditor')),
            'copy_editor' => SimpleUserResource::make($this->whenLoaded('copyEditor')),
            'fact_checker' => SimpleUserResource::make($this->whenLoaded('factChecker')),
            'translator' => SimpleUserResource::make($this->whenLoaded('translator')),
            'created_at' => $this->created_at,
            'pitched_at' => $this->pitch->created_at,
            'pitch_id' => $this->pitch->getPublicKey(),
            'country' => $country,
            'default_language' => $this->default_language,
            'comments_count' => $this->whenLoaded('comments_count', fn() => $this->comments_count),
            'watcher_ids' => $this->whenLoaded('watchers', fn() => $this->watchers?->pluck('user_id')),
            ...$additional,
        ];

        return $data;
    }
}
