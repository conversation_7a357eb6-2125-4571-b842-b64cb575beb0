<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Users\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AttachmentsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Media $media */
        $media = $this->resource;

        $urlRoot = 'betterflow/v1/editorials/packages/';
        $searchToken = 'packages/';

        $conversions = $this->getGeneratedConversions()->map(function ($cond, $conversion) use ($media) {
            if ($cond) {
                return [
                    'key' => $conversion,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'path' => $media->getPathRelativeToRoot($conversion),
                    'url' => $media->getUrl($conversion),
                ];
            }
        });

        return [
            'id' => $media->getKey(),
            'name' => $media->name,
            'model_id' => $media->model_id,
            'model_type' => $media->model_type,
            'mime_type' => $media->mime_type,
            'size' => $media->size,
            'original_path' => $media->getPath(),
            'conversions' => $conversions,
            'full_url' => str($media->getUrl())->replaceFirst($searchToken, $urlRoot)->prepend(config('app.url')),
            'url' => $media->getFullUrl(),
            'preview_url' => $media->preview_url,
            'uploaded_by' => SimpleUserResource::make(User::find($media->getCustomProperty('uploaded_by'))),
        ];
    }
}
