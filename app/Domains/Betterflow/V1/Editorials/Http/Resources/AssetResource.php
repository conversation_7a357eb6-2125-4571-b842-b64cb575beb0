<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Comments\Http\Resources\CommentsResource;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class AssetResource extends JsonResource
{
    public function toArray($request): array
    {
        $latestVersion = $this->content()->where('language_code', $request->language ?? 'en-EN')->first()?->currentVersion();

        $data = unserialize(data_get($latestVersion, 'model_data', null));
        $responsibleUser = null;

        if ($latestVersion && $latestVersion->user_id) {
            $responsibleUser = SimpleUserResource::make($latestVersion->responsibleUser);
        }


        $allTasks = $this->resource->loadMissing('tasks.taskable.assignee', 'tasks.user', 'assignee')->tasks;

        $assetTasks = collect();
        $ownerTask = null;

        if ($allTasks?->isNotEmpty()) {
            $assetTasks = $allTasks->filter(function ($task) {
                return ! $task->isOwnerTask();
            });

            $ownerTask = $allTasks->filter(function ($task) {
                return $task->isOwnerTask();
            })->first();
        }

        return [
            'id' => $this->getResourceKey(),
            'editorial_id' => $this->whenLoaded('editorial', fn() => $this->editorial?->getResourceKey()),
            'name' => $this->name,
            'slug' => $this->slug,
            'url' => $this->url,
            'required' => $this->required,
            'type' => $this->resource->type->value,
            'description' => $this->description,
            'completed' => $this->isComplete(),
            'schema_version' => $this->schema_version,
            'assignee' => SimpleUserResource::make($this->whenLoaded('assignee')),
            'updated_by' => $this->when($responsibleUser, fn() => $responsibleUser),
            'content' => $this->when($data, [
                'id' => data_get($data, 'id'),
                'language' => data_get($data, 'language_code'),
                ...Arr::wrap(data_get($data, 'data')),
            ], null),
            $this->mergeWhen($allTasks, [
                'tasks' => $this->when($assetTasks->isNotEmpty(), TaskResource::collection($assetTasks->groupBy('status')->map)->additional(['asset_id' => $this->getResourceKey()])),
                'owner_task' => $this->whenNotNull(TaskResource::make($ownerTask)->additional(['asset_id' => $this->getResourceKey()])),
            ]),
            'comments' => CommentsResource::collection($this->whenLoaded('comments')),
        ];
    }
}
