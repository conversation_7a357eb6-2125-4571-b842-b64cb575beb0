<?php
namespace App\Domains\Betterflow\V1\Editorials\Http\Requests;

use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class PackageAttachmentsRequest extends FormRequest
{
    public function authorize(#[RouteParameter('package')] EditorialPackage $package)
    {
        return Gate::authorize('attach_media', $package);
    }
   
    public function rules()
    {
        return [
            'attachments' => ['required','array'],
            'attachments.*' => ['file'],
        ];
    }

    public function getAttachments()
    {
        return $this->validated();
    }


}