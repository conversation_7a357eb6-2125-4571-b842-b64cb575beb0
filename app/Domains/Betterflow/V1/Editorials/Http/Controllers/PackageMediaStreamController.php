<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use App\Domains\Shared\Concerns\StreamsMedia;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PackageMediaStreamController extends Controller
{
    use StreamsMedia;

    public function streamByFilename(Request $request, EditorialPackage $package, Media $media)
    {
        // abort_unless($package->getMedia()->contains($media), 404, 'Attachment not found');

        return $this->streamMedia($media);
    }
}
