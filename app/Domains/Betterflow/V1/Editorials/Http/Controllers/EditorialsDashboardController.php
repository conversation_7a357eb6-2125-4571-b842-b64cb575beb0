<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EditorialsDashboardController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $tasks = $request->user()->pendingTasks()->with('taskable')->wherein('taskable_type', ['editorial', 'asset'])->get();

        $editorials = $request->user()->editorials()->get();

        $assignedEditorials = Editorial::query()->with('assignee')->whereRelation('assignee', 'assigned_to_id', $request->user()->getKey())->get();

        $editorials = $editorials->merge($assignedEditorials);

        return response()->json(
            [
                'tasks' => TaskResource::collection($tasks),
                'editorials' => EditorialResource::collection($editorials),
            ],
        );
    }
}
