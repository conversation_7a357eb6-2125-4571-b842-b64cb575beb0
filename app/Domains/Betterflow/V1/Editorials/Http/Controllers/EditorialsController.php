<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Resources\Json\ResourceCollection;

class EditorialsController extends Controller
{
    public function index()
    {
        return EditorialResource::collection(
            Editorial::query()->with([
                'ownTasks' => fn($query) => $query->with([
                    'user' => fn($query) => $query->with('roles', 'sessions'),
                    'taskable'
                ])->latest(),
                'assists' => fn($query) => $query->with([
                    'user' => fn($query) => $query->with('roles', 'sessions'),
                    'requestedBy' => fn($query) => $query->with('roles', 'sessions'),
                    'taskable'
                ])->latest(),
                'tasksForPhase' => fn($query) => $query->with([
                    'user' => fn($query) => $query->with('roles', 'sessions'),
                    'taskable'
                ])->latest(),
                'watchers' => fn($query) => $query->with([
                    'user' => fn($query) => $query->with('roles', 'sessions')
                ]),
                'reporter' => fn($query) => $query->with('roles', 'sessions'),
                'assignee' => fn($query) => $query->with('roles', 'sessions'),
                'toplineEditor' => fn($query) => $query->with('roles', 'sessions'),
                'copyEditor' => fn($query) => $query->with('roles', 'sessions'),
                'factChecker' => fn($query) => $query->with('roles', 'sessions'),
                'translator' => fn($query) => $query->with('roles', 'sessions'),
                'packages',
                'pitch' => fn($query) => $query->with('topic', 'vertical', 'type'),
            ])->withCount('comments')->get()
        );
    }

    public function show(Editorial $editorial): EditorialResource
    {
        return EditorialResource::make($editorial->loadMissing([
            'comments.replies',
            'assets.assignee',
            'assets' => fn($query) => $query->with(['tasks.user' => fn($query) => $query->with('roles', 'session')]),
            'tasksForPhase',
            'ownTasks' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
            'pendingTasks' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
            'assists' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'requestedBy' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
            'watchers' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session')]),
            'assignee' => fn($query) => $query->with('roles', 'session'),
            'reporter' => fn($query) => $query->with('roles', 'session'),
            'translator' => fn($query) => $query->with('roles', 'session'),
            'toplineEditor' => fn($query) => $query->with('roles', 'session'),
            'copyEditor' => fn($query) => $query->with('roles', 'session'),
            'factChecker' => fn($query) => $query->with('roles', 'session'),
            'packages',
            'pitch' => fn($query) => $query->with('topic', 'vertical', 'type'),
        ])->loadCount('comments'));
    }
}
