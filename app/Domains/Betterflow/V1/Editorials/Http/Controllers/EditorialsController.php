<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Resources\Json\ResourceCollection;

class EditorialsController extends Controller
{
    public function index()
    {
        $editorials = Editorial::query()->with([
            'ownTasks' => fn($query) => $query->with(['taskable'])->latest(),
            'assists' => fn($query) => $query->with(['taskable'])->latest(),
            'tasksForPhase' => fn($query) => $query->with(['taskable'])->latest(),
            'watchers',
            'reporter',
            'assignee',
            'toplineEditor',
            'copyEditor',
            'factChecker',
            'translator',
            'packages',
            'pitch' => fn($query) => $query->with('topic', 'vertical', 'type'),
        ])->withCount('comments')->get();

        // Collect all unique user IDs from all relationships
        $userIds = collect();

        foreach ($editorials as $editorial) {
            // Direct user relationships
            if ($editorial->reporter_id) $userIds->push($editorial->reporter_id);
            if ($editorial->assigned_to_id) $userIds->push($editorial->assigned_to_id);
            if ($editorial->topline_editor_id) $userIds->push($editorial->topline_editor_id);
            if ($editorial->copy_editor_id) $userIds->push($editorial->copy_editor_id);
            if ($editorial->fact_checker_id) $userIds->push($editorial->fact_checker_id);
            if ($editorial->translator_id) $userIds->push($editorial->translator_id);

            // Task users
            foreach ($editorial->ownTasks as $task) {
                if ($task->user_id) $userIds->push($task->user_id);
            }
            foreach ($editorial->assists as $task) {
                if ($task->user_id) $userIds->push($task->user_id);
                if ($task->requested_by_id) $userIds->push($task->requested_by_id);
            }
            foreach ($editorial->tasksForPhase as $task) {
                if ($task->user_id) $userIds->push($task->user_id);
            }

            // Watcher users
            foreach ($editorial->watchers as $watcher) {
                if ($watcher->user_id) $userIds->push($watcher->user_id);
            }
        }

        // Load all users with their roles and sessions in one query
        $users = \App\Domains\Users\Models\User::whereIn('id', $userIds->unique())
            ->with(['roles', 'sessions'])
            ->get()
            ->keyBy('id');

        // Manually set the loaded relationships to avoid additional queries
        foreach ($editorials as $editorial) {
            // Set direct user relationships
            if ($editorial->reporter_id && isset($users[$editorial->reporter_id])) {
                $editorial->setRelation('reporter', $users[$editorial->reporter_id]);
            }
            if ($editorial->assigned_to_id && isset($users[$editorial->assigned_to_id])) {
                $editorial->setRelation('assignee', $users[$editorial->assigned_to_id]);
            }
            if ($editorial->topline_editor_id && isset($users[$editorial->topline_editor_id])) {
                $editorial->setRelation('toplineEditor', $users[$editorial->topline_editor_id]);
            }
            if ($editorial->copy_editor_id && isset($users[$editorial->copy_editor_id])) {
                $editorial->setRelation('copyEditor', $users[$editorial->copy_editor_id]);
            }
            if ($editorial->fact_checker_id && isset($users[$editorial->fact_checker_id])) {
                $editorial->setRelation('factChecker', $users[$editorial->fact_checker_id]);
            }
            if ($editorial->translator_id && isset($users[$editorial->translator_id])) {
                $editorial->setRelation('translator', $users[$editorial->translator_id]);
            }

            // Set task user relationships
            foreach ($editorial->ownTasks as $task) {
                if ($task->user_id && isset($users[$task->user_id])) {
                    $task->setRelation('user', $users[$task->user_id]);
                }
            }
            foreach ($editorial->assists as $task) {
                if ($task->user_id && isset($users[$task->user_id])) {
                    $task->setRelation('user', $users[$task->user_id]);
                }
                if ($task->requested_by_id && isset($users[$task->requested_by_id])) {
                    $task->setRelation('requestedBy', $users[$task->requested_by_id]);
                }
            }
            foreach ($editorial->tasksForPhase as $task) {
                if ($task->user_id && isset($users[$task->user_id])) {
                    $task->setRelation('user', $users[$task->user_id]);
                }
            }

            // Set watcher user relationships
            foreach ($editorial->watchers as $watcher) {
                if ($watcher->user_id && isset($users[$watcher->user_id])) {
                    $watcher->setRelation('user', $users[$watcher->user_id]);
                }
            }
        }

        return EditorialResource::collection($editorials);
    }

    public function show(Editorial $editorial): EditorialResource
    {
        return EditorialResource::make($editorial->loadMissing([
            'comments.replies',
            'assets.assignee',
            'assets' => fn($query) => $query->with(['tasks.user' => fn($query) => $query->with('roles', 'session')]),
            'tasksForPhase',
            'ownTasks' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
            'pendingTasks' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
            'assists' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'requestedBy' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
            'watchers' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session')]),
            'assignee' => fn($query) => $query->with('roles', 'session'),
            'reporter' => fn($query) => $query->with('roles', 'session'),
            'translator' => fn($query) => $query->with('roles', 'session'),
            'toplineEditor' => fn($query) => $query->with('roles', 'session'),
            'copyEditor' => fn($query) => $query->with('roles', 'session'),
            'factChecker' => fn($query) => $query->with('roles', 'session'),
            'packages',
            'pitch' => fn($query) => $query->with('topic', 'vertical', 'type'),
        ])->loadCount('comments'));
    }
}
