<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\CollaboratorsAdded;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Pitches\Notifications\CollaboratorAdded;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandleCollaboratorsAdded implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(CollaboratorsAdded $event): void
    {
        $pitch = Pitch::find($event->pitchId);
        $collaborators = $pitch->collaborators->whereIn('id', $event->collaborators);

        if ($collaborators->isNotEmpty()) {
            $collaborators->each(function ($user) use ($pitch): void {
                $user->notify(new CollaboratorAdded($pitch->getKey()));
            });
        }
    }
}
