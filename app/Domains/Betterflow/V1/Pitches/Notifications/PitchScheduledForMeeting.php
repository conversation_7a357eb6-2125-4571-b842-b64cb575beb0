<?php

namespace App\Domains\Betterflow\V1\Pitches\Notifications;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationPitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\NotificationAction;
use App\Domains\Shared\Data\NotificationDTO;
use App\Domains\Shared\Enums\MessageType;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Date;

class PitchScheduledForMeeting extends Notification
{
    use Queueable;

    public function __construct(
        public string $pitchId,
    ) {}

    public function via($notifiable): array
    {
        return ['database'];
    }

    public function toArray($notifiable): array
    {
        $pitch = Pitch::find($this->pitchId);
        $meeting = $pitch->meeting();

        $start = Date::createFromDate($meeting->start?->getDateTime());

        return (new NotificationDTO(
            title: $pitch->short_name.' has been added to the agenda for the '.$start->format('d/m/Y').' pitch meeting',
            message: 'Your pitch will be discussed in the upcoming pitch meeting. Please ensure all necessary information is up to date.',
            forWhat: NotificationPitchResource::make($pitch)->additional(['type' => 'pitch']),
            type: $this->databaseType($notifiable),
            origin: config('lighthouse.betterflow.namespace'),
            user: $notifiable,
            action: new NotificationAction(
                severity: MessageType::Muted,
                buttonLabel: 'View Pitch',
                message: 'View Pitch Details',
            ),
        ))->toArray();
    }

    public function databaseType(object $notifiable): string
    {
        return 'pitch_scheduled_for_meeting';
    }
}
