<?php

namespace App\Domains\Betterflow\V1\Pitches\Notifications;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationPitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\NotificationAction;
use App\Domains\Shared\Data\NotificationDTO;
use App\Domains\Shared\Enums\MessageType;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class ReviewTravelPitch extends Notification
{
    use Queueable;

    public function __construct(
        public string $pitchId,
    ) {}

    public function via($notifiable): array
    {
        return ['database'];
    }

    public function toArray($notifiable): array
    {
        $pitch = Pitch::find($this->pitchId);

        return (new NotificationDTO(
            title: $pitch->short_name.' has been sent for travel review.',
            message: 'Your pitch requires travel review.',
            forWhat: NotificationPitchResource::make($pitch)->additional(['type' => 'pitch']),
            type: $this->databaseType($notifiable),
            origin: config('lighthouse.betterflow.namespace'),
            user: $notifiable,
            action: new NotificationAction(
                severity: MessageType::Muted,
                buttonLabel: 'Review Pitch',
                message: 'View Pitch Details',
            ),
        ))->toArray();
    }

    public function databaseType(object $notifiable): string
    {
        return 'pitch_sent_for_travel_review';
    }
}
