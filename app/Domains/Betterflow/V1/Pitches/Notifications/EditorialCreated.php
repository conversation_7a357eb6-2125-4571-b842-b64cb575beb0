<?php

namespace App\Domains\Betterflow\V1\Pitches\Notifications;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\NotificationAction;
use App\Domains\Shared\Data\NotificationDTO;
use App\Domains\Shared\Enums\MessageType;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class EditorialCreated extends Notification
{
    use Queueable;

    public function __construct(
        public string $pitchId,
        public ?string $createdBy,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        $pitch = Pitch::find($this->pitchId);
        $editorial = $pitch->editorial;

        return (new NotificationDTO(
            title: $pitch->short_name . ' has been approved.',
            message: 'Your pitch has been approved and converted to an Editorial. You can now proceed with content creation.',
            forWhat: EditorialResource::make($editorial)->additional(['type' => 'editorial']),
            type: $this->databaseType($notifiable),
            origin: config('lighthouse.betterflow.namespace'),
            user: $notifiable,
            action: new NotificationAction(
                severity: MessageType::Muted,
                buttonLabel: 'View Editorial',
                message: 'View Editorial',
            ),
        ))->toArray();
    }

    public function databaseType(object $notifiable): string
    {
        return 'editorial_created';
    }
}
