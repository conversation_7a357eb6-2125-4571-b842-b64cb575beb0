<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Listeners;

use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskAdded;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendsNotificationWhenTaskIsAdded implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    public function handle(TaskAdded $event)
    {
        $task = $event->task;

        $notificationType = data_get($task->data, 'properties.notification_type', $task->type);

        $task->user->notify(new DefaultNotification(
            forWhat: $task->taskable,
            title: $task->title,
            message: $task->description,
            type: $notificationType,
        ));
    }
}
