<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Stringable;

class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $subject = $this->taskable;

        $editorialId = data_get($this->additional, 'editorial_id', null);
        $assetId = data_get($this->additional, 'asset_id', null);

        if ($subject instanceof Editorial) {
            $editorialId = $subject->getResourceKey();
        }

        if ($subject instanceof Asset) {
            $assetId = $subject->getResourceKey();
        }

        return [
            'id' => $this->getResourceKey(),
            'title' => $this->title,
            'description' => $this->description,
            'completed' => $this->completed() ?? false,
            'progress' => $this->progress,
            'last_updated_at' => $this->when(! $this->completed(), $this->updated_at),
            'created_at' => $this->created_at,
            'completed_at' => $this->when($this->completed(), $this->completed_at),
            'due_at' => $this->when($this->due_at, $this->due_at),
            'assignee' => SimpleUserResource::make($this->whenLoaded('user')),
            'status' => $this->status,
            'type' => $this->type,
            'data' => $this->when($subject, [
                // 'subject' => $subject,
                'subject_type' => $this->subjectType($subject)->toString(),
                'subject_slug' => $this->subjectType($subject, slug: true)->toString(),
                'subject_parent' => $this->subjectType($subject, true),
                'subject_id' => $this->subjectKey($subject),
                'editorial_id' => $this->when($editorialId, fn() => $editorialId),
                'asset_id' => $this->when($assetId, fn() => $assetId),
            ]),
            'notes' => $this->notes,
            'instructions' => data_get($this->data, 'properties.instructions', null),
            'action' => data_get($this->data, 'action', null),
            'requested_by' => $this->whenLoaded('requestedBy', fn() => SimpleUserResource::make($this->requestedBy)),
        ];
    }

    private function subjectType($subject, bool $parent = false, bool $slug = false): string|Stringable
    {
        if ($parent) {
            return $subject->getMorphClass();
        }

        return str(class_basename($subject::class))
            ->snake()
            ->when(
                $slug,
                fn(Stringable $value) => $value->slug()
            )->lower();
    }

    private function subjectKey($subject): int|string
    {
        return match ($subject->getMorphClass()) {
            'pitch', 'editorial', 'asset' => $subject->getPublicId(),
            default => $subject->getResourceKey(),
        };
    }
}
