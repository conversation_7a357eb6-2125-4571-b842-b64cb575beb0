<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Enums;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\TaskType;

enum EditorialTaskType: string implements TaskType
{
    case AssignEditors = 'assign_editors';
    case AssignFactCheckerAndCopyEditor = 'assign_fact_checker_and_copy_editor';
    case Plan = 'plan';
    case ToplineEdit = 'approve_topline_edit';
    case CopyEdit = 'copy_edit';
    case FactCheck = 'fact_check';
    case AssignTranslator = 'assign_translator';
    case Translation = 'translation';
    case RequestedAssist = 'requested_assist';
    case FinalReview = 'final_review';

    public function phase(): EditorialSubPhase
    {
        return match ($this) {
            self::AssignEditors => EditorialSubPhase::EditingAssignment,
            self::AssignFactCheckerAndCopyEditor => EditorialSubPhase::FactCheckAssignment,
            self::Plan => EditorialSubPhase::Editing,
            self::ToplineEdit => EditorialSubPhase::ToplineEdit,
            self::CopyEdit => EditorialSubPhase::CopyEdit,
            self::FactCheck => EditorialSubPhase::FactCheck,
            self::AssignTranslator => EditorialSubPhase::TranslationAssignment,
            self::Translation => EditorialSubPhase::Translation,
            self::RequestedAssist => EditorialSubPhase::FinalReview,
            self::FinalReview => EditorialSubPhase::FinalReview,
        };
    }
}
