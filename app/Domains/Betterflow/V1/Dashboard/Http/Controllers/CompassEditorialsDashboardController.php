<?php

namespace App\Domains\Betterflow\V1\Dashboard\Http\Controllers;

use App\Domains\Users\Models\User;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CompassEditorialsDashboardController
{
    public function __invoke(#[CurrentUser] User $user): AnonymousResourceCollection
    {
        return EditorialResource::collection($user->editorials);
    }
}
