<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Domains\Betterflow\V1\Admin\Crud\Models{
/**
 * Country model representing geographical regions and locations.
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Betterflow\V1\Admin\Crud\Models\TFactory|null $use_factory
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Country whereUpdatedAt($value)
 */
	class Country extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Admin\Crud\Models{
/**
 * EditorialStage model representing stages in the editorial workflow.
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string $slug
 * @property bool $active
 * @property string $type
 * @property string $color
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @method static \Database\Factories\Admin\Crud\EditorialStageFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialStage withoutTrashed()
 */
	class EditorialStage extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Admin\Crud\Models{
/**
 * PitchHoldReason model representing reasons for pausing pitch progression.
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string|null $color
 * @property string|null $description
 * @property bool $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @method static \App\Domains\Betterflow\V1\Admin\Crud\Database\Factories\PitchHoldReasonFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchHoldReason withoutTrashed()
 */
	class PitchHoldReason extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Admin\Crud\Models{
/**
 * PitchStage model representing progression stages for content pitches.
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string $slug
 * @property bool $active
 * @property string $type
 * @property string $color
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @method static \App\Domains\Betterflow\V1\Admin\Crud\Database\Factories\PitchStageFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchStage withoutTrashed()
 */
	class PitchStage extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Admin\Crud\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string|null $description
 * @property bool $active
 * @property string $color
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $icon
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @method static \App\Domains\Betterflow\V1\Admin\Crud\Database\Factories\PitchTypeFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchType withoutTrashed()
 */
	class PitchType extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset withoutTrashed()
 */
	class Asset extends \Eloquent implements \App\Domains\Shared\Contracts\HasPublicKey {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * AssetContent model representing detailed content for editorial assets.
 *
 * @property int $id
 * @property int $asset_id
 * @property string $language_code
 * @property array<array-key, mixed>|null $data
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset|null $asset
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent whereAssetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent whereLanguageCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssetContent withoutTrashed()
 */
	class AssetContent extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Fact withoutTrashed()
 */
	class Fact extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Facts withoutTrashed()
 */
	class Facts extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Graphic withoutTrashed()
 */
	class Graphic extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HeadlineSummary withoutTrashed()
 */
	class HeadlineSummary extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Illustration withoutTrashed()
 */
	class Illustration extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Photo withoutTrashed()
 */
	class Photo extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporting withoutTrashed()
 */
	class Reporting extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Research withoutTrashed()
 */
	class Research extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property int $editorial_id
 * @property int|null $assigned_to_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $description
 * @property string|null $url
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\AssetType $type
 * @property bool $required
 * @property string|null $attributes
 * @property string $schema_version
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent> $content
 * @property-read int|null $content_count
 * @property string $default_language
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $owner
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source complete()
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereEditorialId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereSchemaVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Source withoutTrashed()
 */
	class Source extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string $assignable_type
 * @property int $assignable_id
 * @property \Spatie\Permission\Models\Role|null $role
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $assignable
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $user
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\AssignmentFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereAssignableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereAssignableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Assignment withoutTrashed()
 */
	class Assignment extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property string $name
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase $phase
 * @property \App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase|null $sub_phase
 * @property int $pitch_id
 * @property string $default_language
 * @property int|null $reporter_id
 * @property int|null $assigned_to_id
 * @property int|null $topline_editor_id
 * @property int|null $copy_editor_id
 * @property int|null $fact_checker_id
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $publish_date
 * @property int|null $translator_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset> $assets
 * @property-read int|null $assets_count
 * @property-read \App\Domains\Users\Models\User|null $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assignment> $assignments
 * @property-read int|null $assignments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset> $completedAssets
 * @property-read int|null $completed_assets_count
 * @property-read \App\Domains\Users\Models\User|null $copyEditor
 * @property-read \App\Domains\Users\Models\User|null $factChecker
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\Pitch|null $pitch
 * @property-read \App\Domains\Users\Models\User|null $reporter
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \App\Domains\Users\Models\User|null $toplineEditor
 * @property-read \App\Domains\Users\Models\User|null $translator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \GPJ\Watchable\Models\Watch> $watchers
 * @property-read int|null $watchers_count
 * @method static \App\Domains\Betterflow\V1\Editorials\Database\Factories\EditorialFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereAssignedToId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereCopyEditorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereDefaultLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereFactCheckerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial wherePhase($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial wherePitchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial wherePublicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial wherePublishDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereReporterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereSubPhase($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereToplineEditorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereTranslatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editorial withoutTrashed()
 */
	class Editorial extends \Eloquent implements \App\Domains\Shared\Contracts\HasState, \App\Domains\Shared\Contracts\HasPublicKey {}
}

namespace App\Domains\Betterflow\V1\Editorials\Models{
/**
 * 
 *
 * @property int $id
 * @property string $statable_type
 * @property int $statable_id
 * @property string $state
 * @property string|null $attributes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $statable
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState whereStatableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState whereStatableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialState whereUpdatedAt($value)
 */
	class EditorialState extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Pitches\Models{
/**
 * 
 *
 * @property int $id
 * @property string $public_id
 * @property string|null $short_name
 * @property string|null $slug
 * @property string|null $country_code
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $pitch_type
 * @property string|null $primary_language
 * @property int|null $assigned_to
 * @property int|null $topic_id
 * @property int|null $vertical_id
 * @property string $form_schema_version
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Users\Models\User|null $assigned
 * @property-read \App\Domains\Users\Models\User $assignee
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assists
 * @property-read int|null $assists_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $attachments
 * @property-read int $attachments_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $attachmentsCount
 * @property-read \App\Domains\Users\Models\User $author
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $collaborator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Users\Models\User> $collaborators
 * @property-read int|null $collaborators_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Comments\Models\Comment> $comments
 * @property-read int $comments_count
 * @property-read \App\Domains\Comments\Models\Comment|null $commentsCount
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchState|null $current_state
 * @property-read mixed $current_state_id
 * @property-read \App\Domains\Betterflow\V1\Editorials\Models\Editorial|null $editorial
 * @property-read mixed $first_state
 * @property-read mixed $first_state_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\PitchForm> $forms
 * @property-read int|null $forms_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\TFactory|null $use_factory
 * @property-read int|null $watchers_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \GlobalPress\Events\Models\EventPitch|null $pitchEvent
 * @property-read mixed $previous_state
 * @property-read mixed $previous_state_id
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchForm|null $primaryForm
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\PitchState> $revisions
 * @property-read int|null $revisions_count
 * @property-write mixed $reason
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchState|null $state
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\PitchState> $states
 * @property-read int|null $states_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasksOnHold
 * @property-read int|null $tasks_on_hold_count
 * @property-read \App\Domains\Topics\Models\Topic|null $topic
 * @property-read \App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType|null $type
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @property-read \App\Domains\Verticals\Models\Vertical|null $vertical
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \GPJ\Watchable\Models\Watch> $watchers
 * @property-read \GPJ\Watchable\Models\Watch|null $watchersCount
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch drafts()
 * @method static \App\Domains\Betterflow\V1\Pitches\Database\Factories\PitchFactory factory($count = null, $state = [])
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch findByPublicId(string $publicId)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch newModelQuery()
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch newQuery()
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch notApproved()
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch notDrafts()
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch notOwnedBy(?string $userId = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pitch onlyTrashed()
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch ownDrafts(string $userId)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch ownedByUser(string $userId)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch query()
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereAssignedTo($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereCountryCode($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereCreatedAt($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereCreatedBy($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereDeletedAt($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereFormSchemaVersion($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereId($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch wherePitchType($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch wherePrimaryLanguage($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch wherePublicId($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereShortName($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereSlug($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereTopicId($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereUpdatedAt($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch whereVerticalId($value)
 * @method static \App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder<static>|Pitch withStates(...$states)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pitch withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Pitch withoutTrashed()
 */
	class Pitch extends \Eloquent implements \App\Domains\Shared\Contracts\HasPublicKey, \App\Domains\Shared\Contracts\HasState, \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Domains\Betterflow\V1\Pitches\Models{
/**
 * 
 *
 * @property int $pitch_id
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\Pitch $pitch
 * @property-read \App\Domains\Users\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchCollaborator newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchCollaborator newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchCollaborator query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchCollaborator whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchCollaborator wherePitchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchCollaborator whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchCollaborator whereUserId($value)
 */
	class PitchCollaborator extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Pitches\Models{
/**
 * 
 *
 * @property int $id
 * @property string $language_code
 * @property int $pitch_id
 * @property array<array-key, mixed>|null $form
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\Pitch $pitch
 * @property-write mixed $reason
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Mpociot\Versionable\Version> $versions
 * @property-read int|null $versions_count
 * @method static \Database\Factories\PitchFormFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm whereForm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm whereLanguageCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm wherePitchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchForm withoutTrashed()
 */
	class PitchForm extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Pitches\Models{
/**
 * 
 *
 * @property int $id
 * @property string $statable_type
 * @property int $statable_id
 * @property \App\Domains\Betterflow\V1\Pitches\Enums\PitchStage $state
 * @property array<array-key, mixed>|null $attributes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $statable
 * @property-read \App\Domains\Betterflow\V1\Admin\Crud\Models\PitchStage|null $stateModel
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState whereStatableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState whereStatableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PitchState whereUpdatedAt($value)
 */
	class PitchState extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Projects\Models{
/**
 * Project model representing collaborative work and project tracking.
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Project withoutTrashed()
 */
	class Project extends \Eloquent {}
}

namespace App\Domains\Betterflow\V1\Shared\Tasks\Models{
/**
 * Task model representing workflow tasks and assignments.
 *
 * @property int $id
 * @property string $title
 * @property string|null $description
 * @property int $user_id
 * @property string $taskable_type
 * @property int $taskable_id
 * @property \App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus $status
 * @property string $type
 * @property string|null $notes
 * @property string $progress
 * @property array<array-key, mixed>|null $data
 * @property \Illuminate\Support\Carbon|null $due_at
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property int|null $requested_by_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Users\Models\User|null $requestedBy
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $taskable
 * @property-read \App\Domains\Users\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task activeAndRecent()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task completed()
 * @method static \App\Domains\Betterflow\V1\Shared\Database\Factories\TaskFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task onHold()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereDueAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereProgress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereRequestedById($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereTaskableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereTaskableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Task whereUserId($value)
 */
	class Task extends \Eloquent {}
}

namespace App\Domains\Comments\Models{
/**
 * Comment model representing user-generated comments and discussions.
 *
 * @property int $id
 * @property array<array-key, mixed> $comment
 * @property int $user_id
 * @property int|null $parent_id
 * @property string $commentable_type
 * @property int $commentable_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $commentable
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Comment> $replies
 * @property-read int|null $replies_count
 * @property-read \App\Domains\Users\Models\User $user
 * @method static \Database\Factories\Comments\CommentFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereCommentableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereCommentableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereHasMark(\Maize\Markable\Mark $mark, \Illuminate\Database\Eloquent\Model $user, ?string $value = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Comment whereUserId($value)
 */
	class Comment extends \Eloquent {}
}

namespace App\Domains\Shared\Models{
/**
 * 
 *
 * @property int $id
 * @property string|null $log_name
 * @property string $description
 * @property string|null $subject_type
 * @property int|null $subject_id
 * @property string|null $causer_type
 * @property int|null $causer_id
 * @property \Illuminate\Support\Collection<array-key, mixed>|null $properties
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $event
 * @property string|null $batch_uuid
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent|null $causer
 * @property-read \Illuminate\Support\Collection $changes
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent|null $subject
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity causedBy(\Illuminate\Database\Eloquent\Model $causer)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity forBatch(string $batchUuid)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity forEvent(string $event)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity forEvents(array $events)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity forSubject(\Illuminate\Database\Eloquent\Model $subject)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity hasBatch()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity inLog(...$logNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereBatchUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCauserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCauserType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereLogName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereProperties($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereSubjectType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereUpdatedAt($value)
 */
	class Activity extends \Eloquent {}
}

namespace App\Domains\Shared\Models{
/**
 * 
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BasePivot newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BasePivot newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BasePivot query()
 */
	class BasePivot extends \Eloquent {}
}

namespace App\Domains\Shared\Models{
/**
 * 
 *
 * @property int $id
 * @property int $user_id
 * @property string $markable_type
 * @property int $markable_id
 * @property string|null $value
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $markable
 * @property-read \App\Domains\Users\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereMarkableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereMarkableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommentLike whereValue($value)
 */
	class CommentLike extends \Eloquent {}
}

namespace App\Domains\Shared\Models{
/**
 * Session model tracking user authentication and interaction sessions.
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property string $payload
 * @property int $last_activity
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session whereLastActivity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session whereUserAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Session whereUserId($value)
 */
	class Session extends \Eloquent {}
}

namespace App\Domains\Spaces\Models{
/**
 * Setting model representing configurable system or space parameters.
 *
 * @property int $id
 * @property string $key
 * @property string $value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $space_id
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Spaces\Models\Space|null $space
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Setting whereValue($value)
 */
	class Setting extends \Eloquent {}
}

namespace App\Domains\Spaces\Models{
/**
 * Space model representing collaborative workspaces or environments.
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string $description
 * @property string|null $permissions
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Spaces\Models\TFactory|null $use_factory
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Spaces\Models\Setting> $settings
 * @property-read int|null $settings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Users\Models\User> $users
 * @property-read int|null $users_count
 * @method static \App\Domains\Spaces\Database\Factories\SpaceFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space wherePermissions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Space whereUpdatedAt($value)
 */
	class Space extends \Eloquent {}
}

namespace App\Domains\Topics\Models{
/**
 * Topic model representing content subject areas and themes.
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string|null $color
 * @property string|null $description
 * @property bool $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $assigned_editor_id
 * @property string|null $icon
 * @property-read \App\Domains\Users\Models\User|null $editor
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Verticals\Models\Vertical> $verticals
 * @property-read int|null $verticals_count
 * @method static \Database\Factories\Topics\TopicFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereAssignedEditorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Topic whereUpdatedAt($value)
 */
	class Topic extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin withoutTrashed()
 */
	class Admin extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison active()
 * @method static \App\Domains\Users\Database\Factories\AudienceLiaisonFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AudienceLiaison withoutTrashed()
 */
	class AudienceLiaison extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CopyEditor withoutTrashed()
 */
	class CopyEditor extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \App\Domains\Topics\Models\Topic|null $topic
 * @property-read \App\Domains\Verticals\Models\Vertical|null $vertical
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor active()
 * @method static \App\Domains\Users\Database\Factories\EditorFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor topicEditor()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor verticalEditor()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Editor withoutTrashed()
 */
	class Editor extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EditorialAdmin withoutTrashed()
 */
	class EditorialAdmin extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FactChecker withoutTrashed()
 */
	class FactChecker extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GraphicsEditor withoutTrashed()
 */
	class GraphicsEditor extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * Impersonation model tracking user impersonation events.
 *
 * @property int $id
 * @property int $personal_access_token_id
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation wherePersonalAccessTokenId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Impersonation whereUserId($value)
 */
	class Impersonation extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Interpreter withoutTrashed()
 */
	class Interpreter extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ManagingEditor withoutTrashed()
 */
	class ManagingEditor extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor active()
 * @method static \App\Domains\Users\Database\Factories\PhotoEditorFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PhotoEditor withoutTrashed()
 */
	class PhotoEditor extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter active()
 * @method static \App\Domains\Users\Database\Factories\ReporterFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reporter withoutTrashed()
 */
	class Reporter extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Researcher withoutTrashed()
 */
	class Researcher extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SuperAdmin withoutTrashed()
 */
	class SuperAdmin extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|System withoutTrashed()
 */
	class System extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator active()
 * @method static \App\Domains\Users\Database\Factories\TranslatorFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Translator withoutTrashed()
 */
	class Translator extends \Eloquent {}
}

namespace App\Domains\Users\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $api_token
 * @property string|null $photo_url
 * @property bool $active
 * @property string|null $job_title
 * @property string $local_language
 * @property string|null $bio
 * @property string|null $location
 * @property string|null $location_city
 * @property string|null $slack_id
 * @property mixed $timezone
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $type
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property array<array-key, mixed>|null $preferences
 * @property int|null $current_space_id
 * @property $locale
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Shared\Models\Activity> $actions
 * @property-read int|null $actions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $assetTasks
 * @property-read int|null $asset_tasks_count
 * @property-read \App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $collaboratingPitches
 * @property-read int|null $collaborating_pitches_count
 * @property-read \App\Domains\Spaces\Models\Space|null $currentSpace
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $editorialTasks
 * @property-read int|null $editorial_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Editorials\Models\Editorial> $editorials
 * @property-read int|null $editorials_count
 * @property-read array|null $qr_code
 * @property-read array|string|null $recovery_codes
 * @property-read bool $two_factor_enabled
 * @property-read \App\Domains\Users\Models\TFactory|null $use_factory
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $ownPitches
 * @property-read int|null $own_pitches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingAssists
 * @property-read int|null $pending_assists_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pendingTasks
 * @property-read int|null $pending_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Media|null $photo
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $pitchTasks
 * @property-read int|null $pitch_tasks_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Domains\Shared\Models\Session|null $session
 * @property-read \App\Domains\Shared\Models\Session|null $sessions
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Shared\Tasks\Models\Task> $tasks
 * @property-read int|null $tasks_count
 * @property-read mixed $timezone_readable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Domains\Betterflow\V1\Pitches\Models\Pitch> $watchedPitches
 * @property-read int|null $watched_pitches_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User active()
 * @method static \App\Domains\Users\Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User notSystem()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCurrentSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLocalLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLocationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhotoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRoleNames(...$roleNames)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereSlackId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutTrashed()
 */
	class User extends \Eloquent implements \Laravel\Sanctum\Contracts\HasApiTokens, \Spatie\MediaLibrary\HasMedia {}
}

namespace App\Domains\Verticals\Models{
/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string|null $description
 * @property string $color
 * @property bool $active
 * @property int|null $assigned_editor_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $topic_id
 * @property string|null $icon
 * @property-read \App\Domains\Users\Models\User|null $editor
 * @property-read \App\Domains\Shared\Models\TFactory|null $use_factory
 * @property-read \App\Domains\Topics\Models\Topic|null $topic
 * @method static \Database\Factories\Verticals\VerticalFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereAssignedEditorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereTopicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vertical withoutTrashed()
 */
	class Vertical extends \Eloquent {}
}

