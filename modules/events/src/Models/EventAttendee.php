<?php

namespace GlobalPress\Events\Models;

use App\Domains\Shared\Models\BasePivot;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventAttendee extends BasePivot
{
    public $timestamps = false;

    protected $table = 'event_attendees';

    protected $fillable = [
        'event_id',
        'user_id',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function newCollection(array $models = []): EventAttendeeCollection
    {
        return new EventAttendeeCollection($models);
    }
}
