<?php

namespace GlobalPress\Events\Models;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Collection;

class EventAttendeeCollection extends Collection
{
    public function mapWithAttendees($callback = null)
    {
        return $this->map(function (EventAttendee $event) use ($callback) {
            $event->event = Event::find($event->event_id)->googleEvent;
            $lighthouseUsers = User::whereIn('email', collect($event->event->attendees)->pluck('email'))->get();
            $attendees = $lighthouseUsers->map(function ($attendee) {
                $user = User::where('email', $attendee->email)->first();

                return SimpleUserResource::make($user);
            });

            $event->atendees = $attendees;
            $event->user = SimpleUserResource::make($event->user);

            return $event;
        });   
    }
}
