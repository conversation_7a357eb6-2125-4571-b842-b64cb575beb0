#!/bin/bash

# Deployment script for Lighthouse API

# Main deployment process
main() {

    # Install PHP dependencies
    log "Installing/updating Composer dependencies..."
    composer install --no-interaction --prefer-dist --optimize-autoloader 

    # Laravel specific preparations
    log "Preparing Laravel application..."
    php artisan config:clear
    php artisan cache:clear
    php artisan route:clear
    php artisan view:clear

    # Run database migrations
    log "Running database migrations..."
    php artisan migrate --force

    # Optimize Laravel application
    log "Optimizing application..."
    php artisan optimize

    log "Deployment scripts run successfully! 🚀"
}

# Execute main deployment function
main