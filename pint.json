{"preset": "laravel", "rules": {"simplified_null_return": true, "array_indentation": true, "new_with_parentheses": {"anonymous_class": true, "named_class": true}, "class_definition": {"multi_line_extends_each_single_line": false, "single_line": false, "space_before_parenthesis": true}, "declare_strict_types": true, "final_class": true, "final_internal_class": {"allow_inheritdoc": true, "break_up_large_classes": true, "keep_inheritance": true, "single_abstract_class": true, "include": ["internal", "InternalService", "Service", "Repository"], "exclude": ["Migration", "Seeder", "Factory", "AbstractController", "BaseController", "BaseService", "BaseRepository"], "consider_absent_docblock_as_internal_class": false}, "global_namespace_import": {"import_classes": true, "import_constants": true, "import_functions": true}, "method_chaining_indentation": true, "no_unused_imports": true, "ordered_imports": {"imports_order": ["class", "function", "const"], "sort_algorithm": "alpha"}, "php_unit_method_casing": {"case": "snake_case"}, "return_type_declaration": {"space_before": "none"}, "strict_comparison": true, "strict_param": true}, "exclude": ["vendor", "bootstrap", "storage", "node_modules"], "notName": ["*Test.php", "*Migration.php", "*Seeder.php"]}