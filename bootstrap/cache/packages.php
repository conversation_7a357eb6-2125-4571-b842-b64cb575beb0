<?php return array (
  'barryvdh/laravel-ide-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    ),
  ),
  'dedoc/scramble' => 
  array (
    'providers' => 
    array (
      0 => 'Dedoc\\Scramble\\ScrambleServiceProvider',
    ),
  ),
  'globalpress/events' => 
  array (
    'providers' => 
    array (
      0 => 'GlobalPress\\Events\\Providers\\EventsServiceProvider',
    ),
  ),
  'globalpress/watchable' => 
  array (
    'providers' => 
    array (
      0 => 'GPJ\\Watchable\\WatchableServiceProvider',
    ),
  ),
  'hisorange/browser-detect' => 
  array (
    'aliases' => 
    array (
      'Browser' => 'hisorange\\BrowserDetect\\Facade',
    ),
    'providers' => 
    array (
      0 => 'hisorange\\BrowserDetect\\ServiceProvider',
    ),
  ),
  'internachi/modular' => 
  array (
    'aliases' => 
    array (
      'Modules' => 'InterNACHI\\Modular\\Support\\Facades\\Modules',
    ),
    'providers' => 
    array (
      0 => 'InterNACHI\\Modular\\Support\\ModularServiceProvider',
      1 => 'InterNACHI\\Modular\\Support\\ModularizedCommandsServiceProvider',
      2 => 'InterNACHI\\Modular\\Support\\ModularEventServiceProvider',
    ),
  ),
  'larabug/larabug' => 
  array (
    'providers' => 
    array (
      0 => 'LaraBug\\ServiceProvider',
    ),
  ),
  'laradumps/laradumps' => 
  array (
    'providers' => 
    array (
      0 => 'LaraDumps\\LaraDumps\\LaraDumpsServiceProvider',
    ),
  ),
  'laravel/fortify' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Fortify\\FortifyServiceProvider',
    ),
  ),
  'laravel/horizon' => 
  array (
    'aliases' => 
    array (
      'Horizon' => 'Laravel\\Horizon\\Horizon',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Horizon\\HorizonServiceProvider',
    ),
  ),
  'laravel/octane' => 
  array (
    'aliases' => 
    array (
      'Octane' => 'Laravel\\Octane\\Facades\\Octane',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Octane\\OctaneServiceProvider',
    ),
  ),
  'laravel/reverb' => 
  array (
    'aliases' => 
    array (
      'Output' => 'Laravel\\Reverb\\Output',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Reverb\\ApplicationManagerServiceProvider',
      1 => 'Laravel\\Reverb\\ReverbServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/telescope' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Telescope\\TelescopeServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'lorisleiva/laravel-actions' => 
  array (
    'aliases' => 
    array (
      'Action' => 'Lorisleiva\\Actions\\Facades\\Actions',
    ),
    'providers' => 
    array (
      0 => 'Lorisleiva\\Actions\\ActionServiceProvider',
    ),
  ),
  'lorisleiva/lody' => 
  array (
    'aliases' => 
    array (
      'Lody' => 'Lorisleiva\\Lody\\Lody',
    ),
    'providers' => 
    array (
      0 => 'Lorisleiva\\Lody\\LodyServiceProvider',
    ),
  ),
  'lunarstorm/laravel-ddd' => 
  array (
    'aliases' => 
    array (
      'DDD' => 'Lunarstorm\\LaravelDDD\\Facades\\DDD',
    ),
    'providers' => 
    array (
      0 => 'Lunarstorm\\LaravelDDD\\LaravelDDDServiceProvider',
    ),
  ),
  'maize-tech/laravel-markable' => 
  array (
    'providers' => 
    array (
      0 => 'Maize\\Markable\\MarkableServiceProvider',
    ),
  ),
  'mpociot/versionable' => 
  array (
    'providers' => 
    array (
      0 => 'Mpociot\\Versionable\\Providers\\ServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'spatie/laravel-activitylog' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Activitylog\\ActivitylogServiceProvider',
    ),
  ),
  'spatie/laravel-backup' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Backup\\BackupServiceProvider',
    ),
  ),
  'spatie/laravel-data' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\LaravelData\\LaravelDataServiceProvider',
    ),
  ),
  'spatie/laravel-google-calendar' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\GoogleCalendar\\GoogleCalendarServiceProvider',
    ),
    'aliases' => 
    array (
      'GoogleCalendar' => 'Spatie\\GoogleCalendar\\GoogleCalendarFacade',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
  ),
  'spatie/laravel-medialibrary' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'spatie/laravel-signal-aware-command' => 
  array (
    'aliases' => 
    array (
      'Signal' => 'Spatie\\SignalAwareCommand\\Facades\\Signal',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\SignalAwareCommand\\SignalAwareCommandServiceProvider',
    ),
  ),
  'spatie/laravel-validation-rules' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\ValidationRules\\ValidationRulesServiceProvider',
    ),
  ),
  'spatie/laravel-web-tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\WebTinker\\WebTinkerServiceProvider',
    ),
  ),
  'spatie/php-structure-discoverer' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\StructureDiscoverer\\StructureDiscovererServiceProvider',
    ),
  ),
  'staudenmeir/laravel-adjacency-list' => 
  array (
    'providers' => 
    array (
      0 => 'Staudenmeir\\LaravelAdjacencyList\\IdeHelperServiceProvider',
    ),
  ),
  'staudenmeir/laravel-cte' => 
  array (
    'providers' => 
    array (
      0 => 'Staudenmeir\\LaravelCte\\DatabaseServiceProvider',
    ),
  ),
  'wnx/laravel-backup-restore' => 
  array (
    'aliases' => 
    array (
      'LaravelBackupRestore' => 'Wnx\\LaravelBackupRestore\\Facades\\LaravelBackupRestore',
    ),
    'providers' => 
    array (
      0 => 'Wnx\\LaravelBackupRestore\\LaravelBackupRestoreServiceProvider',
    ),
  ),
);