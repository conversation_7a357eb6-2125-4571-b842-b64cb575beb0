const chokidar = require('chokidar');
const { spawn } = require('child_process');
const path = require('path');

// Directories to watch
const watchDirs = [
    './app',
    './tests',
    './config',
    './routes'
];

// Ignore patterns
const ignored = [
    /(^|[\/\\])\../, // Ignore dotfiles
    /node_modules/,
    /vendor/,
    /\.git/,
    /\.(swp|swo)$/, // Ignore swap files
    /\.(log|tmp)$/
];

// Mapping of plural to singular forms
const pluralToSingular = (part) => {
    return {
        'Editorials': 'Editorial',
    'Assets': 'Asset',
    'Users': 'User',
    'Comments': 'Comment',
    'Tasks': 'Task',
    'Pitches': 'Pitch',
    'Users' : 'User' }[part] ?? part;
    // Add more mappings as needed
};

/// Function to extract domain and filename details from file path
function extractTestFilter(filePath) {
    // Match the domain name and get the filename
    const parts = filePath.split('/')
        .map((part) => path.extname(part) === '.php' ? part.replace('.php', '') : part)
        .map((part) => pluralToSingular(part));
    const domainMatch = filePath.match(/app\/Domains\/([^/]+)/);
    const fileNameMatch = path.basename(filePath, path.extname(filePath));
    
    console.log('🔍 Extracted details:', {
        pathParts: filePath.split('/'),
        domain: domainMatch ? domainMatch[1] : null,
        fileName: fileNameMatch
    });
    
    if (domainMatch) {
        const domain = domainMatch[1];
        // Convert to singular if exists
        const singularDomain = pluralToSingular(domain);
        
        // Prepare filter parts
        const filters = [];
        
        // Add domain filter
        filters.push(singularDomain);
        console.log([filters, parts].join('|'));
        
        // Add first part of filename (if it's not the same as domain)
        const firstPart = fileNameMatch.split(/(?=[A-Z])/, 1)[0];
        if (firstPart && firstPart !== singularDomain) {
            filters.push(pluralToSingular[firstPart]);
        }
        

        return [...filters, ...parts].join('|');
    }
    return parts.join('|');
}

// Function to run tests
function runTests(changedFile = null) {
    console.clear(); // Clear console before each test run
    console.log(`🕰️  Running tests at ${new Date().toLocaleTimeString()}...`);
    
    const start = Date.now();
    
    // Prepare test command
    let testCommand = ['artisan', 'test'];
    
    // If a specific domain is changed, filter tests to that domain
    if (changedFile) {
        const filter = extractTestFilter(changedFile);
        if (filter) {
            console.log(`🎯 Filtering tests with regex: ${filter}`);
            testCommand.push(`--filter="/${filter}/"`);
        }
    } else {
        console.log(`🎯 Running all tests`);
    }

    const test = spawn('php', testCommand, { 
        stdio: 'inherit',
        shell: true 
    });

    test.on('close', (code) => {
        const duration = Date.now() - start;
        if (code === 0) {
            console.log(`\n✅ All tests passed in ${duration}ms`);
        } else {
            console.log(`\n❌ Tests failed with exit code ${code} in ${duration}ms`);
        }
        console.log('🔍 Continuing to watch for changes...');
    });
}


// Initialize watcher
const watcher = chokidar.watch(watchDirs, {
    ignored,
    persistent: true,
    ignoreInitial: true,
    depth: Infinity
});

// Function to determine if file is in a domain
function isInDomain(filePath) {
    return filePath.includes('app/Domains/') || filePath.includes('tests/Feature/Domains/');
}

// Add event listeners
watcher
    .on('ready', () => console.log('🔍 Watching for file changes...'))
    .on('change', (path) => {
        console.log(`📝 File changed: ${path}`);
        if (isInDomain(path)) {
            runTests(path);
        } else {
            runTests();
        }
    })
    .on('add', (path) => {
        console.log(`➕ File added: ${path}`);
        if (isInDomain(path)) {
            runTests(path);
        }
    })
    .on('unlink', (path) => {
        console.log(`➖ File deleted: ${path}`);
        if (isInDomain(path)) {
            runTests(path);
        }
    });

// Handle process termination
process.on('SIGINT', () => {
    watcher.close();
    console.log('\n🛑 Watching stopped.');
    process.exit(0);
});