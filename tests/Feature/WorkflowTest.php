<?php

namespace Tests\Feature;

use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\Reporter;
use App\Domains\Verticals\Enums\Verticals;
use App\Domains\Verticals\Models\Vertical;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class WorkflowTest extends TestCase
{
    use FastRefreshDatabase;

    protected $seed = true;

    #[Test]
    public function sample_workflow(): void
    {

        $this->markTestIncomplete();
        $reporter = Reporter::where('email', '<EMAIL>')->first() ?? Reporter::factory()
            ->verified()
            ->create([
                'name' => 'Gamuchirai Masiyiwa',
                'email' => '<EMAIL>',
                'bio' => "<PERSON><PERSON><PERSON><PERSON> is a Global Press Journal reporter based in Harare, Zimbabwe. She is an internationally acclaimed economy and education reporter. Born and raised in Zimbabwe's capital city, <PERSON><PERSON><PERSON><PERSON> holds a diploma in journalism and mass communication and a bachelor’s degree with honors in political science. Her 2019 coverage of currencies introduced in Zimbabwe over the last decade, presented in comic form, won a Clarion Award for best feature journalism. When she's not reporting, Gamu spends her time touring ancient ruins throughout Zimbabwe.",
                'job_title' => 'Reporter-in-Residence',
                'local_language' => 'en-EN',
                'location' => 'ZW',
                'timezone' => TimeHelper::timezone('Africa/Harare'),
                'slack_id' => 'U3HBNR94Y',
                'photo_url' => 'https://ca.slack-edge.com/T03Q4CLA8-U3HBNR94Y-811db84b130c-192',
            ]);

        $topic = Topic::where('slug', 'global-health')->first();
        $vertical = Vertical::where('slug', Verticals::The_Mass_Exodus)->first();

        $pitchResponse = $this->actingAs($reporter)->postJson(route('betterflow.v1.pitches.create'), [
            'working_headline' => 'Test123',
            'short_name' => 'Why Specialists are Leaving Zimbabwe',
            'slug' => 'why-specialists-are-leaving-zimbabwe',
            'angle_statement' => '',
            'form' => [],
            'pitch_type' => 1,
            'country_code' => 'ZW',
            'topic' => $topic->getKey(),
            'vertical' => $vertical->getKey(),
        ]);

        $pitchResponse->assertOk();

        $this->assertEquals(PitchStage::Draft, $pitchResponse->json('data.stage'));
    }
}
