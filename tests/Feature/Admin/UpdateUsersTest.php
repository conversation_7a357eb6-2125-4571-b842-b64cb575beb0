<?php

namespace Tests\Feature\Admin;

use App\Domains\Users\Models\User;
use Database\Seeders\Crud\RoleSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class UpdateUsersTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RoleSeeder::class);

        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Original Name',
        ]);

        $this->actingAs($this->admin);
    }

    public function test_admin_can_update_user_basic_info(): void
    {
        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'bio' => 'New bio',
            'job_title' => 'New Position',
            'location' => 'New Location',
            'timezone' => 'Europe/London',
            'slack_id' => 'U123456',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('user.name', 'Updated Name')
            ->assertJsonPath('user.email', '<EMAIL>')
            ->assertJsonPath('user.bio', 'New bio')
            ->assertJsonPath('user.job_title', 'New Position')
            ->assertJsonPath('user.location', 'New Location')
            ->assertJsonPath('user.timezone.name', 'Europe/London')
            ->assertJsonPath('user.slack_id', 'U123456');

        $this->assertDatabaseHas('users', [
            'id' => $this->user->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_admin_can_update_user_roles(): void
    {
        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'roles' => ['editor'],
        ]);

        $response->assertStatus(200);
        $this->assertTrue($this->user->fresh()->hasRole('editor'));
    }

    public function test_admin_cannot_update_own_roles(): void
    {
        $response = $this->putJson(route('admin.users.update', $this->admin->id), [
            'roles' => ['editor'],
        ]);

        $response->assertStatus(200);
        $this->assertTrue($this->admin->fresh()->hasRole('admin'));
        $this->assertFalse($this->admin->fresh()->hasRole('editor'));
    }

    public function test_admin_can_update_user_photo(): void
    {

        $this->markTestIncomplete('This test is incomplete photo not working.');
        Storage::fake('public');

        $file = UploadedFile::fake()->image('avatar.jpg');

        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'photo' => $file,
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('user.photo_url', $file);

        Storage::disk('r2')->assertExists($this->user->fresh()->photo_url);
    }

    public function test_validates_email_format(): void
    {
        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_validates_company_email(): void
    {
        $this->markTestSkipped('No longer a requirement');
        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_validates_unique_email(): void
    {
        User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_validates_timezone(): void
    {
        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'timezone' => 'Invalid/Timezone',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['timezone']);
    }

    public function test_validates_password_confirmation(): void
    {
        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'password' => 'password123',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password_confirmation']);
    }

    public function test_validates_roles(): void
    {
        $response = $this->putJson(route('admin.users.update', $this->user->id), [
            'roles' => ['invalid-role'],
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['roles.0']);
    }
}
