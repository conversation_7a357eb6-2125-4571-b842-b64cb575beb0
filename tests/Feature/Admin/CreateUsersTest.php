<?php

namespace Tests\Feature\Admin;

use App\Domains\Users\Events\UserCreated;
use App\Domains\Users\Listeners\SendConfirmationEmail;
use App\Domains\Users\Models\User;
use App\Domains\Users\Notifications\ConfirmAccount;
use Database\Seeders\Crud\RoleSeeder;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class CreateUsersTest extends TestCase
{
    use FastRefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RoleSeeder::class);
    }

    #[Test]
    public function admin_can_create_users(): void
    {
        $usersCount = User::query()->count();

        Notification::fake([
            ConfirmAccount::class,
        ]);
        Queue::fake();
        Event::fake([
            UserCreated::class,
        ]);

        $userAttributes = User::factory()->make([
            'email' => '<EMAIL>',
        ])->toArray();

        $this->actingAsAdmin();

        $response = $this->postJson(route('admin.users.store'), [
            ...$userAttributes,
            'timezone' => 'Europe/London',
            'roles' => ['editor'],
        ]);

        $listener = new SendConfirmationEmail();

        /** @var User */
        $user = User::query()->where('email', $userAttributes['email'])->first();
        $listener->handle(new UserCreated($user));

        $this->assertTrue($user->hasVerifiedEmail());

        $response->assertSuccessful();

        Notification::assertSentTo($user, ConfirmAccount::class, function (ConfirmAccount $notification, $channels, $notifiable) use ($user) {
            /** @var \Illuminate\Notifications\Messages\MailMessage $message */
            $message = $notification->toMail($user);

            $this->assertContains('mail', $channels);

            $this->assertEquals('Confirm account', $message->actionText);
            $this->assertStringContainsString(config('app.frontend_url'), $message->actionUrl);

            return $notifiable->is($user);
        });

        $this->assertDatabaseHas('users', [
            'email' => $userAttributes['email'],
        ]);

        $this->assertEquals(['editor'], $user->roles->pluck('name')->toArray());

        $this->assertDatabaseCount('users', $usersCount + 2);
    }
}
