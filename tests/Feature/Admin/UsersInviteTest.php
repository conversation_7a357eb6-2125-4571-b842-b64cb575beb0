<?php

namespace Tests\Feature\Admin;

use App\Domains\Users\Mail\InviteUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class UsersInviteTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_invite_user(): void
    {
        Mail::fake();

        $this->actingAsAdmin();

        $response = $this->postJson(route('admin.users.invite'), [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);

        Mail::assertSent(InviteUser::class, function ($mail) {
            return $mail->hasTo('<EMAIL>');
        });
    }

    public function test_email_is_required(): void
    {
        $this->actingAsAdmin();
        $response = $this->postJson(route('admin.users.invite'), []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_email_must_be_valid(): void
    {
        $this->actingAsAdmin();
        $response = $this->postJson(route('admin.users.invite'), [
            'email' => 'not-an-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }
}
