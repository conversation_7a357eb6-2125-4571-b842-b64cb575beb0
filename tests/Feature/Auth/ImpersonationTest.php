<?php

namespace Tests\Feature\Auth;

use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\Admin;
use App\Domains\Users\Models\Impersonation;
use App\Domains\Users\Models\User;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class ImpersonationTest extends TestCase
{
    use FastRefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(PermissionsSeeder::class);
        $this->seed(RoleSeeder::class);
    }

    #[Test]
    public function it_allows_admins_to_impersonate_users(): void
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable|User $admin */
        $admin = Admin::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        /** @var \Illuminate\Contracts\Auth\Authenticatable|User $user */
        $user = User::factory()->create([]);
        $user->givePermissionTo(Permission::BE_IMPERSONATED);

        $response = $this->postJson(route('auth.login'), [
            'email' => $admin->email,
            'password' => 'password',
        ]);

        $response->assertOk();

        $loginToken = $response->json('token');

        $response = $this->actingAs($admin, 'api')->getJson(route('user.impersonate', $user), [
            'Authorization' => 'Bearer ' . $loginToken
        ]);

        $response->assertOk();

        $impersonateToken = $response->json('token');

        $this->assertAuthenticatedAs($admin, 'api');
        $this->assertAuthenticatedAs($admin, 'sanctum');

        $this->assertDatabaseHas('impersonations', [
            'user_id' => $admin->getKey(),
            'personal_access_token_id' => str($impersonateToken)->before('|')
        ]);

        $impersonation = Impersonation::where('user_id', $admin->getKey())->first();

        $this->assertDatabaseCount('impersonations', 1);

        $this->assertDatabaseHas('users', [
            'id' => $admin->getKey(),
            'api_token' => hash('sha256', $loginToken)
        ]);

        $this->assertDatabaseHas('users', [
            'id' => $user->getKey(),
            'api_token' => hash('sha256', $impersonateToken)
        ]);

        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_id' => $admin->getKey(),
            'tokenable_type' => $admin->getRoleName(),
            'name' => 'lighthouse'
        ]);

        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_id' => $user->getKey(),
            'tokenable_type' => $user->getRoleName(),
            'name' => 'IMPERSONATION token'
        ]);

        $this->actingAs($user, 'api');

        $response = $this->getJson(route('betterflow.v1.dashboard.tasks'), [
            'Authorization' => 'Bearer ' . $impersonateToken
        ]);

        $response->assertOk();

        $response = $this->getJson(route('user.impersonate.leave'), [
            'Authorization' => 'Bearer ' . $impersonateToken
        ]);

        $newAdminToken = $response->json('token');

        $this->assertDatabaseMissing('impersonations', [
            'user_id' => $admin->getKey(),
            'personal_access_token_id' => str($impersonateToken)->before('|')
        ]);

        $this->assertDatabaseMissing('users', [
            'id' => $user->getKey(),
            'api_token' => hash('sha256', $impersonateToken)
        ]);

        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_id' => $admin->getKey(),
            'tokenable_type' => $admin->getRoleName(),
            'name' => 'lighthouse',
            'token' => hash('sha256', str($newAdminToken)->after('|'))
        ]);

        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $admin->getKey(),
            'tokenable_type' => $admin->getRoleName(),
            'name' => 'lighthouse',
            'token' => hash('sha256', str($loginToken)->after('|'))
        ]);
    }
}
