<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Reporting;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\Editor;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Response;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EditorialsTest extends TestCase
{
    private Editorial $editorial;
    /** @var Authenticatable $user */
    private Editor $user;

    public function setUp(): void
    {
        parent::setUp();
        $this->seed(PermissionsSeeder::class); 
        $this->seed(RoleSeeder::class); 

        $this->user = Editor::factory()->create();

        $pitch = Pitch::factory()->create([
            'pitch_type' => PitchType::factory(),
        ]);
        $this->editorial = Editorial::factory()->create([
            'pitch_id' => $pitch->getKey(),
            'sub_phase' => EditorialSubPhase::stagesForPhase(EditorialPhase::Editing)[0],
        ]);

        $this->editorial->toplineEditor()->associate($this->user);
        $this->editorial->factCHecker()->associate($this->user);
        $this->editorial->copyEditor()->associate($this->user);
        $this->editorial->translator()->associate($this->user);
        $this->editorial->assignee()->associate($this->user);
        $this->editorial->save();

        $this->editorial->tasks()->create(Task::factory()->make([
            'user_id' => $this->user,
            'type' => EditorialTaskType::AssignTranslator,
        ])->toArray());

        $this->editorial->assets()->create(Reporting::factory()->make([
            'assigned_to_id' => $this->user,
        ])->toArray());
    }

    #[Test]
    public function it_can_get_all_editorials(): void
    {
        $this->actingAs($this->user);

        $response = $this->get(route('betterflow.v1.editorials.index'));

        $response->assertOk();
    }

    #[Test]
    public function it_can_get_an_editorial(): void
    {

        $this->actingAs($this->user);
        
        $response = $this->get(route('betterflow.v1.editorials.show', ['editorial' => $this->editorial]));

        $response->assertOk();
    }

    #[Test]
    public function it_requires_attachments_to_store(): void
    {
        $this->actingAs($this->user);
        
        $package = $this->editorial->packages()->create([
            'language_code' => 'en-EN',
            'data' => []
        ]);

        $response = $this->postJson(route('betterflow.v1.editorials.packages.attachments.store', [
            'editorial' => $this->editorial,
            'package' => $package
        ]), []);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
