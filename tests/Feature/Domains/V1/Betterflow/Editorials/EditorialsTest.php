<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Reporting;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\Editor;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class EditorialsTest extends TestCase
{
    private Editorial $editorial;
    /** @var Authenticatable $user */
    private Editor $user;

    public function setUp(): void
    {
        parent::setUp();
        $this->seed(PermissionsSeeder::class);
        $this->seed(RoleSeeder::class);

        $this->user = Editor::factory()->create();

        $pitch = Pitch::factory()->create([
            'pitch_type' => PitchType::factory(),
        ]);
        $this->editorial = Editorial::factory()->create([
            'pitch_id' => $pitch->getKey(),
            'sub_phase' => EditorialSubPhase::stagesForPhase(EditorialPhase::Editing)[0],
        ]);

        $this->editorial->toplineEditor()->associate($this->user);
        $this->editorial->factCHecker()->associate($this->user);
        $this->editorial->copyEditor()->associate($this->user);
        $this->editorial->translator()->associate($this->user);
        $this->editorial->assignee()->associate($this->user);
        $this->editorial->save();

        $this->editorial->tasks()->create(Task::factory()->make([
            'user_id' => $this->user,
            'type' => EditorialTaskType::AssignTranslator,
        ])->toArray());

        $this->editorial->assets()->create(Reporting::factory()->make([
            'assigned_to_id' => $this->user,
        ])->toArray());
    }

    #[Test]
    public function it_can_get_all_editorials(): void
    {
        $this->actingAs($this->user);

        $response = $this->get(route('betterflow.v1.editorials.index'));

        $response->assertOk();
    }

    #[Test]
    public function it_can_get_an_editorial(): void
    {

        $this->actingAs($this->user);

        $response = $this->get(route('betterflow.v1.editorials.show', ['editorial' => $this->editorial]));

        $response->assertOk();
    }

    #[Test]
    public function it_requires_attachments_to_store(): void
    {
        $this->actingAs($this->user);

        $package = $this->editorial->packages()->create([
            'language_code' => 'en-EN',
            'data' => []
        ]);

        $response = $this->postJson(route('betterflow.v1.editorials.packages.attachments.store', [
            'editorial' => $this->editorial,
            'package' => $package
        ]), []);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    #[Test]
    #[DataProvider('editoralCountProvider')]
    public function it_does_not_have_n_plus_one_queries_when_listing_editorials(int $editorialCount = 10): void
    {
        $this->actingAs($this->user);

        // Create multiple editorials with different users to test N+1 scenario
        $users = Editor::factory()->count(5)->create();
        $editorials = Editorial::factory()->count($editorialCount)->create();

        // Assign different users to different roles for each editorial
        foreach ($editorials as $index => $editorial) {
            $user = $users[$index % 5];
            $editorial->assignee()->associate($user);
            $editorial->reporter()->associate($user);
            $editorial->toplineEditor()->associate($user);
            $editorial->save();
        }

        // Clear any existing cache to ensure fresh queries
        \Illuminate\Support\Facades\Cache::flush();

        // Clear query log to start fresh for the actual request
        DB::flushQueryLog();
        DB::enableQueryLog();

        $response = $this->get(route('betterflow.v1.editorials.index'));

        $queries = DB::getQueryLog();
        DB::disableQueryLog();

        $response->assertOk();

        // Count role-related queries - should be minimal due to eager loading
        $roleQueries = collect($queries)->filter(function ($query) {
            return str_contains($query['query'], 'model_has_roles');
        });

        // Count user queries
        $userQueries = collect($queries)->filter(function ($query) {
            return str_contains($query['query'], 'select * from "users"') &&
                str_contains($query['query'], 'where "users"."id" in');
        });

        // With proper optimization, we should have exactly 1 role query
        // that loads all user roles in a single query
        $this->assertLessThanOrEqual(
            1,
            $roleQueries->count(),
            'N+1 query issue detected. Expected 1 role query, got ' . $roleQueries->count() .
                '. Queries: ' . $roleQueries->pluck('query')->implode(', ')
        );

        // We should have no additional user queries since we load them optimally
        $this->assertLessThanOrEqual(
            0, // Should be 0 since we load users in the controller optimally
            $userQueries->count(),
            'User N+1 query issue detected. Expected 0 user queries, got ' . $userQueries->count() .
                '. Queries: ' . $userQueries->pluck('query')->implode(', ')
        );

        // Also verify we're not making individual queries per editorial
        $this->assertLessThan(
            count($editorials), // Should be less than the number of editorials
            $roleQueries->count(),
            'Role queries should not scale with number of editorials'
        );
    }

    #[Test]
    public function it_scales_properly_with_more_editorials(): void
    {
        $this->actingAs($this->user);

        // Test with different numbers of editorials to ensure queries don't scale linearly
        $users = Editor::factory()->count(3)->create();

        // Test with 5 editorials first
        $editorials1 = Editorial::factory()->count(5)->create();
        foreach ($editorials1 as $index => $editorial) {
            $user = $users[$index % 3];
            $editorial->assignee()->associate($user);
            $editorial->save();
        }

        \Illuminate\Support\Facades\Cache::flush();
        DB::flushQueryLog();
        DB::enableQueryLog();

        $this->get(route('betterflow.v1.editorials.index'));

        $queries1 = DB::getQueryLog();
        $roleQueries1 = collect($queries1)->filter(fn($q) => str_contains($q['query'], 'model_has_roles'));

        // Now test with 15 editorials (3x more)
        $editorials2 = Editorial::factory()->count(10)->create(); // 10 more = 15 total
        foreach ($editorials2 as $index => $editorial) {
            $user = $users[$index % 3];
            $editorial->assignee()->associate($user);
            $editorial->save();
        }

        \Illuminate\Support\Facades\Cache::flush();
        DB::flushQueryLog();
        DB::enableQueryLog();

        $this->get(route('betterflow.v1.editorials.index'));

        $queries2 = DB::getQueryLog();
        $roleQueries2 = collect($queries2)->filter(fn($q) => str_contains($q['query'], 'model_has_roles'));

        // The number of role queries should not scale linearly with editorials
        // With proper eager loading, it should be roughly the same
        $this->assertLessThanOrEqual(
            $roleQueries1->count() + 2, // Allow small variance
            $roleQueries2->count(),
            'Role queries are scaling with number of editorials. This indicates N+1 issue still exists.'
        );
    }

    public static function editoralCountProvider(): array
    {
        return [
            ['10 Editorials' => fn() => 10],
            ['200 Editorials' => fn() => 200],
            ['300 Editorials' => fn() => 300],
        ];
    }
}
