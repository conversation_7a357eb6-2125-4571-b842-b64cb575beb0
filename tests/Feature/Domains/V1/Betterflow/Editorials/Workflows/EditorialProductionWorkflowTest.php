<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\CopyEditor;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\FactChecker;
use App\Domains\Users\Models\Reporter;
use App\Domains\Users\Models\Translator;
use App\Domains\Users\Models\User;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use Database\Seeders\Roles\AdditonalUsersSeeder;
use Database\Seeders\Roles\AdminSeeder;
use Database\Seeders\Roles\EditorSeeder;
use Database\Seeders\Roles\ReporterSeeder;
use Database\Seeders\Roles\SuperAdminSeeder;
use Illuminate\Support\Facades\Date;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class EditorialProductionWorkflowTest extends TestCase
{
    use FastRefreshDatabase;
    /**
     * Key players
     */
    // NOTE: Vertical Editor, assigned_to_id Assigned editor
    protected $controllingEditor;

    protected $toplineEditor;

    protected $gamuReporter;

    protected $miraFactChecker;

    protected $storyEditor;

    protected $photoEditor;

    protected $copyEditor;

    protected $translator;

    protected Editorial $editorial;

    protected function setUp(): void
    {
        parent::setUp();
        Date::setTestNow(Date::createFromDate('2024-11-10 00:00:00', 'UTC'));

        User::truncate();

        $this->seed([
            RoleSeeder::class,
            PermissionsSeeder::class,
            SuperAdminSeeder::class,
            AdminSeeder::class,
            AdditonalUsersSeeder::class,
            ReporterSeeder::class,
            EditorSeeder::class,
        ]);

        // $superAdmin = SuperAdmin::first();
        //  Ndinda Kioko - Vertical Editor
        $this->controllingEditor = Editor::where('email', '<EMAIL>')->first();
        // $this->controllingEditor->givePermissionTo(Permission::IMPERSONATE);

        // Terry - Topline Editor
        $this->toplineEditor = Editor::where('email', '<EMAIL>')->first();

        // Gamu - Reporter
        $this->gamuReporter = Reporter::where('email', '<EMAIL>')->first();

        // Mira Galanova - Fact Checker
        $this->miraFactChecker = FactChecker::where('email', '<EMAIL>')->first();
        $this->miraFactChecker->givePermissionTo([Permission::CREATE_PITCH, FactChecker::getRolePermissions()]);

        //  ? Translator
        $this->translator = Translator::where('email', '<EMAIL>')->first();
        $this->translator->givePermissionTo([Translator::getRolePermissions()]);

        //  Andrea - Story Editor
        $this->storyEditor = Editor::where('email', '<EMAIL>')->first();
        $this->storyEditor->givePermissionTo([Editor::getRolePermissions()]);

        //  Juan Pablo - Visual Editor
        $this->photoEditor = Editor::where('email', '<EMAIL>')->first();
        $this->photoEditor->givePermissionTo([Editor::getRolePermissions()]);

        //  Allison Braden - Copy Editor
        $this->copyEditor = CopyEditor::where('email', '<EMAIL>')->first();
        $this->copyEditor->givePermissionTo([Editor::getRolePermissions()]);


        $this->editorial = Editorial::factory()->create([
            'phase' => EditorialPhase::Editing,
            'sub_phase' => EditorialSubPhase::ToplineEdit,
            'reporter_id' => $this->gamuReporter->getKey(),
            'assigned_to_id' => $this->controllingEditor->getKey(),
        ]);
    }

    #[Test]
    #[DataProvider('workflowDataProvider')]
    public function it_runs_editorial_workflow_over_multiple_weeks(array $workflows): void
    {
        // Arrange the test
        // $schedule = app()->make(Schedule::class);
        $this->assertDatabaseHas('editorials', [
            'id' => $this->editorial->getKey(),
            'public_id' => $this->editorial->getPublicId(),
        ]);

        if (in_array('translationWorkflow', $workflows)) {
            EditorialPackage::truncate();
            $this->editorial->packages()->createMany([
                ['language_code' => 'en-EN'],
                ['language_code' => 'fr-FR'],
            ]);
        }

        $this->factCheckWorkflow($workflows);
    }

    protected function factCheckWorkflow(array $workflows): void
    {
        /**
         * 1. The Editorial is moved from
         * Phase Editing to Production
         * Sub Phase Fact Check
         */

        $this->travelTo(now()->setTimezone($this->toplineEditor->timezone->name)->setHour(9));

        $this->actingAs($this->toplineEditor);

        $this->toplineEditor->tasks()->create([
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => 'editorial',
            'type' => EditorialTaskType::ToplineEdit,
            'title' => 'Topline Edit',
            'status' => TaskStatus::Pending,
        ]);

        $this->putJson(route('betterflow.v1.common.tasks.complete', [
            'task' => $this->toplineEditor->pendingTasks()->first()->getKey(),
        ]))->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertOk()
            ->assertJsonFragment([
                'phase' => EditorialPhase::Production,
                'sub_phase' => EditorialSubPhase::FactCheckAssignment,
            ]);

        $this->assertDatabaseHas('editorials', [
            'id' => $this->editorial->getKey(),
            'phase' => EditorialPhase::Production,
            'sub_phase' => EditorialSubPhase::FactCheckAssignment,
        ]);

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->controllingEditor->getKey(),
            'title' => 'Assign Fact Checker and Copy Editor',
            'type' => EditorialTaskType::AssignFactCheckerAndCopyEditor,
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->controllingEditor->getKey(),
            'type' => EditorialTaskType::AssignFactCheckerAndCopyEditor
        ]);

        $this->assertCount(1, $this->controllingEditor->refresh()->pendingTasks);

        $this->actingAs($this->controllingEditor)->getJson(route('notifications.index'))
            ->assertOk()
            ->assertJsonFragment([
                'what_happened' => 'Assign Fact Checker and Copy Editor',
                "notification_type" => "assign_fact_checker_and_copy_editor"
            ]);

        /**
         * Ndinda Kioko - Vertical Editor
         *
         * 2. Logs in and
         * Assigns Copy Editor and Fact Cheker to the editorial
         */

        $this->travelTo(now()->setTimezone($this->controllingEditor->timezone->name)->setHour(9));

        $this->actingAs($this->controllingEditor);

        $this->putJson(route('betterflow.v1.editorials.assign', [
            'editorial' => $this->editorial->getPublicId(),
        ]), [
            'users' => [
                [
                    'id' => $this->copyEditor->getKey(),
                    'role' => 'copy_editor',
                ],
                [
                    'id' => $this->miraFactChecker->getKey(),
                    'role' => 'fact_checker',
                ],
            ],
        ])->assertOk();

        $this->travelTo(now()->setTimezone($this->controllingEditor->timezone->name)->setHour(10));

        $this->assertEmpty($this->controllingEditor->refresh()->pendingTasks);
        $this->assertEmpty($this->controllingEditor->refresh()->pendingAssists);


        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertJsonFragment([
                'phase' => EditorialPhase::Production,
                'sub_phase' => EditorialSubPhase::FactCheck,
                // Task
                "title" => "Assign Fact Checker and Copy Editor",
                'type' => EditorialTaskType::AssignFactCheckerAndCopyEditor,
                "status" => TaskStatus::Completed->value
            ])
            ->assertOk();

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->controllingEditor->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'type' => EditorialTaskType::AssignFactCheckerAndCopyEditor,
            'title' => 'Assign Fact Checker and Copy Editor',
            'status' => TaskStatus::Completed,
        ]);

        $this->assertEquals(EditorialTaskType::FactCheck->value, $this->editorial->fresh()->currentTask()->type);

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->miraFactChecker->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'type' => EditorialTaskType::FactCheck,
            'title' => 'Fact check and send to Copy Edit',
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->miraFactChecker->getKey(),
            'type' => 'fact_check'
        ]);

        $this->assertCount(1, $this->editorial->refresh()->pendingTasks);
        $this->assertCount(1, $this->miraFactChecker->refresh()->pendingTasks);

        $this->assertEquals($this->miraFactChecker->getKey(), $this->editorial->fresh()->currentOwner()->getKey());

        /**
         * Mira logs in and
         * requests an assist
         */
        $this->actingAs($this->miraFactChecker);
        $this->travelTo(now()->setTimezone($this->miraFactChecker->timezone->name)->setHour(9));

        $this->postJson(route('betterflow.v1.editorials.editorial.request-assist', [
            'editorial' => $this->editorial->getPublicId(),
        ]), [
            'type' => 'fact_check_review_assist',
            'title' => 'Fact Check Review',
            'description' => "Ndinda, I've left you some comments and questions in the FC",
            'user_id' => $this->controllingEditor->getKey(),
            'due_at' => Date::createFromDate('2024-12-02'),
        ])->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->controllingEditor->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'type' => 'fact_check_review_assist',
            'due_at' => Date::createFromDate('2024-12-02'),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->controllingEditor->getKey(),
            'notifiable_type' => $this->controllingEditor->getMorphClass(),
            'type' => 'fact_check_review_assist',
        ]);

        $this->assertCount(2, $this->editorial->refresh()->pendingTasks);
        $this->assertCount(1, $this->editorial->refresh()->pendingAssists);

        /**
         * Ndinda Kioko - Vertical Editor
         * logs in and
         * completes her assist
         */
        $this->actingAs($this->controllingEditor);
        $this->travelTo(now()->setTimezone($this->controllingEditor->timezone->name)->setHour(14));

        $assistForNdinda = $this->controllingEditor->refresh()->pendingAssists()->first();

        $this->assertEquals('fact_check_review_assist', $assistForNdinda->type);
        $this->assertEquals($this->editorial->getKey(), $assistForNdinda->taskable_id);

        $this->putJson(route('betterflow.v1.editorials.tasks.complete', [
            'editorial' => $this->editorial->getPublicId(),
            'task' => $assistForNdinda->getKey(),
        ]), [
            'notes' => 'Mira, I think I answered all of your questions but one. That needs to go to Gamu.',
        ])->assertOk();

        $response = $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertJsonPath('tasks.completed.1.title', 'Assign Fact Checker and Copy Editor')
            ->assertJsonPath('tasks.completed.1.type', 'assign_fact_checker_and_copy_editor')
            ->assertJsonPath('tasks.completed.1.status', 'completed')

            ->assertJsonPath('assists.completed.0.title', 'Fact Check Review')
            ->assertJsonPath('assists.completed.0.type', 'fact_check_review_assist')
            ->assertJsonPath('assists.completed.0.status', 'completed')

            ->assertJsonPath('tasks.pending.0.title', 'Fact check and send to Copy Edit')
            ->assertJsonPath('tasks.pending.0.type', 'fact_check')
            ->assertJsonPath('tasks.pending.0.status', 'pending')
            ->assertOk();

        $this->assertDatabaseHas('tasks', [
            'id' => $assistForNdinda->getKey(),
            'completed_at' => now()->utc(),
            'status' => TaskStatus::Completed,
        ]);


        // Mira is notified of the completion
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->miraFactChecker->getKey(),
            'notifiable_type' => $this->miraFactChecker->getMorphClass(),
            'type' => 'fact_check_review_assist_completed',
        ]);

        $this->assertCount(1, $this->editorial->refresh()->pendingTasks);
        $this->assertCount(0, $this->editorial->refresh()->pendingAssists);
        $this->assertCount(0, $this->controllingEditor->refresh()->pendingAssists);

        /**
         * Mira logs in and
         * requests an assist
         */
        $this->actingAs($this->miraFactChecker);

        $this->postJson(route('betterflow.v1.editorials.editorial.request-assist', [
            'editorial' => $this->editorial->getPublicId(),
        ]), [
            'type' => 'fact_check_review2_assist',
            'title' => 'Fact Check Review Assist',
            'description' => 'Gamu, I’ve left you one comment on the FC',
            'user_id' => $this->gamuReporter->getKey(),
            'due_at' => Date::createFromDate('2024-12-04'),
        ])->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertJsonPath('tasks.completed.1.title', 'Assign Fact Checker and Copy Editor')
            ->assertJsonPath('tasks.completed.1.type', 'assign_fact_checker_and_copy_editor')
            ->assertJsonPath('tasks.completed.1.status', 'completed')

            ->assertJsonPath('tasks.pending.0.title', 'Fact check and send to Copy Edit')
            ->assertJsonPath('tasks.pending.0.type', 'fact_check')
            ->assertJsonPath('tasks.pending.0.status', 'pending')

            ->assertJsonPath('assists.completed.0.title', 'Fact Check Review')
            ->assertJsonPath('assists.completed.0.type', 'fact_check_review_assist')
            ->assertJsonPath('assists.completed.0.status', 'completed')

            ->assertJsonPath('assists.pending.0.title', 'Fact Check Review Assist')
            ->assertJsonPath('assists.pending.0.type', 'fact_check_review2_assist')
            ->assertJsonPath('assists.pending.0.status', 'pending')

            ->assertOk();

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->gamuReporter->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'type' => 'fact_check_review2_assist',
            'due_at' => Date::createFromDate('2024-12-04'),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->gamuReporter->getKey(),
            'notifiable_type' => $this->gamuReporter->getMorphClass(),
            'type' => 'fact_check_review2_assist',
        ]);

        /**
         * Gamu logs in and
         * completes her assist
         */
        $this->actingAs($this->gamuReporter);

        $assistForGamu = $this->gamuReporter->pendingTasks()->first();
        $this->assertEquals('fact_check_review2_assist', $assistForGamu->type);
        $this->assertEquals($this->editorial->getKey(), $assistForGamu->taskable_id);

        $this->putJson(route('betterflow.v1.editorials.tasks.complete', [
            'editorial' => $this->editorial->getPublicId(),
            'task' => $assistForGamu->getKey(),
        ]))->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertJsonPath('assists.completed.0.title', 'Fact Check Review Assist')
            ->assertJsonPath('assists.completed.0.type', 'fact_check_review2_assist')
            ->assertJsonPath('assists.completed.0.status', 'completed')
            ->assertOk();

        $this->assertDatabaseHas('tasks', [
            'id' => $assistForGamu->getKey(),
            'completed_at' => now()->utc()->format('Y-m-d H:i:s'),
            'status' => TaskStatus::Completed,
        ]);

        // Mira is notified of the completion
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->miraFactChecker->getKey(),
            'notifiable_type' => $this->miraFactChecker->getMorphClass(),
            'type' => 'fact_check_review2_assist_completed',
        ]);

        $this->assertCount(1, $this->editorial->refresh()->pendingTasks);
        $this->assertCount(0, $this->editorial->refresh()->pendingAssists);

        /**
         * Mira logs in and
         * completes her initial owner task
         */
        $this->actingAs($this->miraFactChecker);

        $miraTaskFactCheck = $this->miraFactChecker->pendingTasks->first();

        $this->assertEquals('fact_check', $miraTaskFactCheck->type);

        $this->actingAs($this->miraFactChecker)->putJson(route('betterflow.v1.common.tasks.complete', [
            'task' => $miraTaskFactCheck->getKey(),
        ]))->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertJsonPath('tasks.completed.2.title', 'Fact check and send to Copy Edit')
            ->assertJsonPath('tasks.completed.2.status', 'completed')
            ->assertJsonPath('tasks.completed.2.type', 'fact_check')
            ->assertOk();

        $this->assertDatabaseHas('tasks', [
            'id' => $miraTaskFactCheck->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'completed_at' => now()->utc()->format('Y-m-d H:i:s'),
            'title' => $miraTaskFactCheck->title,
            'type' => $miraTaskFactCheck->type,
            'status' => TaskStatus::Completed,
        ]);

        $this->copyEditWorkflow();
    }

    public function copyEditWorkflow(): void
    {
        /**
         * 2. The Editorial is moved from
         * Phase Production
         * Sub Phase Fact Check
         * to
         * Phase Production
         * Sub Phase Copy Edit
         */

        $this->assertEquals(EditorialPhase::Production, $this->editorial->fresh()->phase);
        $this->assertEquals(EditorialSubPhase::CopyEdit, $this->editorial->fresh()->sub_phase);

        $copyEditorTask = $this->copyEditor->fresh()->pendingTasks()->first();

        $this->assertEquals(EditorialTaskType::CopyEdit->value, $this->editorial->fresh()->currentTask()->type);

        $this->assertEquals(EditorialTaskType::CopyEdit->value, $this->editorial->fresh()->currentTask()->type);

        $this->assertDatabaseHas('tasks', [
            'id' => $copyEditorTask->getKey(),
            'user_id' => $this->copyEditor->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'type' => EditorialTaskType::CopyEdit,
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->copyEditor->getKey(),
            'type' => EditorialTaskType::CopyEdit,
        ]);

        /**
         * Allison logs in and
         * requests an assist
         */
        $this->actingAs($this->copyEditor);

        $this->postJson(route('betterflow.v1.editorials.editorial.request-assist', [
            'editorial' => $this->editorial->getPublicId(),
        ]), [
            'type' => 'copy_edit_review_assist',
            'title' => 'Copy Edit Review',
            'description' => "Ndinda, I've left you some comments and questions in the CE",
            'user_id' => $this->controllingEditor->getKey(),
            'due_at' => Date::createFromDate('2024-12-06'),
        ])->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        //  Ndinda Kioko - Vertical Editor
        // Gets a task and notification
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->controllingEditor->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'type' => 'copy_edit_review_assist',
            'due_at' => Date::createFromDate('2024-12-06'),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->controllingEditor->getKey(),
            'notifiable_type' => $this->controllingEditor->getMorphClass(),
            'type' => 'copy_edit_review_assist',
        ]);

        /**
         * Ndinda Kioko - Vertical Editor
         * logs in and
         * completes her assist
         */
        $this->actingAs($this->controllingEditor);


        $assistForNdinda = $this->controllingEditor->refresh()->pendingAssists()->first();
        $this->assertEquals('copy_edit_review_assist', $assistForNdinda->type);
        $this->assertEquals('Ndinda Kioko', $assistForNdinda->user->name);

        $this->putJson(route('betterflow.v1.editorials.tasks.complete', [
            'editorial' => $this->editorial->getPublicId(),
            'task' => $assistForNdinda->getKey(),
        ]), [
            'notes' => 'Allison, all of your questions are answered',
        ])->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'id' => $assistForNdinda->getKey(),
            'completed_at' => now()->utc()->format('Y-m-d H:i:s'),
            'status' => TaskStatus::Completed,
        ]);

        // Allison gets notified that Ndinda has completed her assist
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->copyEditor->getKey(),
            'notifiable_type' => $this->copyEditor->getMorphClass(),
            'type' => 'copy_edit_review_assist_completed',
        ]);

        /**
         * Allison Logs in and
         * requests an assist
         */
        $this->actingAs($this->copyEditor);

        $this->postJson(route('betterflow.v1.editorials.editorial.request-assist', [
            'editorial' => $this->editorial->getPublicId(),
        ]), [
            'type' => 'copy_edit_review2_assist',
            'title' => 'Copy Edit Review',
            'description' => 'Charlotte, we had to update a caption to reflect a change to the story. Can you review the captions one more time?',
            'user_id' => $this->photoEditor->getKey(),
            'due_at' => Date::createFromDate('2024-12-08'),
        ])->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'taskable_id' => $this->editorial->getKey(),
            'taskable_type' => $this->editorial->getMorphClass(),
            'type' => 'copy_edit_review2_assist',
            'due_at' => Date::createFromDate('2024-12-08'),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->photoEditor->getKey(),
            'notifiable_type' => $this->photoEditor->getMorphClass(),
            'type' => 'copy_edit_review2_assist',
        ]);

        /**
         * Charlotte logs in and
         * completes her assist
         */
        $this->actingAs($this->photoEditor);

        $task = $this->photoEditor->pendingTasks()->first();

        $this->putJson(route('betterflow.v1.editorials.tasks.complete', [
            'editorial' => $this->editorial->getPublicId(),
            'task' => $task->getKey(),
        ]), [
            'notes' => 'Allison, the captions look good',
        ])->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'id' => $task->getKey(),
            'completed_at' => now()->utc()->format('Y-m-d H:i:s'),
            'status' => TaskStatus::Completed,
        ]);

        // Allison gets notified that Charlotte has completed her assist
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->copyEditor->getKey(),
            'notifiable_type' => $this->copyEditor->getMorphClass(),
            'type' => 'copy_edit_review2_assist_completed',
        ]);

        /**
         * Copyeditor: Allison logs in and
         * completes her initial task
         */
        $this->actingAs($this->copyEditor);

        $this->putJson(route('betterflow.v1.common.tasks.complete', [
            'editorial' => $this->editorial->getPublicId(),
            'task' => $copyEditorTask->getKey(),
        ]))->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Allison's task is now completed
        $this->assertDatabaseHas('tasks', [
            'id' => $copyEditorTask->getKey(),
            'completed_at' => now()->utc()->format('Y-m-d H:i:s'),
            'status' => TaskStatus::Completed,
        ]);

        // There are no more pending tasks for the assigned users
        $this->assertEmpty($this->copyEditor->refresh()->pendingTasks);

        if ($this->editorial->fresh()->needsTranslation()) {
            // Create packages for translation
            $this->translationWorkflow();
        } else {
            $this->assertEquals(EditorialPhase::Production, $this->editorial->fresh()->phase);
            $this->assertEquals(EditorialSubPhase::FinalReview, $this->editorial->fresh()->sub_phase);
            $this->finalReviewWorkflow();
        }
    }

    // Optional only when editorial needs translation
    protected function translationWorkflow(): void
    {
        $this->assertEquals(EditorialPhase::Production, $this->editorial->fresh()->phase);
        $this->assertEquals(EditorialSubPhase::TranslationAssignment, $this->editorial->fresh()->sub_phase);

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::AssignTranslator,
            'user_id' => $this->controllingEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->controllingEditor->getKey(),
            'type' => EditorialTaskType::AssignTranslator,
        ]);

        $this->assertDatabaseCount('editorial_packages', 2);
        $this->assertDatabaseHas('editorial_packages', [
            'language_code' => 'en-EN',
        ]);

        $this->assertDatabaseHas('editorial_packages', [
            'language_code' => 'fr-FR',
        ]);

        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->controllingEditor);

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->putJson(route('betterflow.v1.editorials.assign', [
            'editorial' => $this->editorial->getPublicId(),
        ]), [
            'users' => [
                [
                    'id' => $this->translator->getKey(),
                    'role' => 'translator',
                ]
            ]
        ])->assertOk();

        $this->assertEquals(EditorialSubPhase::Translation, $this->editorial->fresh()->sub_phase);

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::AssignTranslator,
            'user_id' => $this->controllingEditor->getKey(),
            'status' => TaskStatus::Completed
        ]);

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::Translation,
            'user_id' => $this->translator->getKey(),
            'status' => TaskStatus::Pending
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->translator->getKey(),
            'type' => EditorialTaskType::Translation,
        ]);

        // Something with translation happens here on the frontend that we don't have access to

        /**
         * Translator logs in and
         * completes their translation task
         */
        $this->actingAs($this->translator);

        $this->putJson(route('betterflow.v1.common.tasks.complete', [
            'editorial' => $this->editorial->getPublicId(),
            'task' => $this->translator->pendingTasks()->first()->getKey(),
        ]))->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::Translation,
            'user_id' => $this->translator->getKey(),
            'status' => TaskStatus::Completed
        ]);

        $this->finalReviewWorkflow();
    }

    public function finalReviewWorkflow(): void
    {
        $this->assertEquals(EditorialPhase::Production, $this->editorial->fresh()->phase);
        $this->assertEquals(EditorialSubPhase::FinalReview, $this->editorial->fresh()->sub_phase);

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::FinalReview,
            'user_id' => $this->controllingEditor->getKey(),
            'status' => TaskStatus::Pending
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->controllingEditor->getKey(),
            'type' => EditorialTaskType::FinalReview,
        ]);

        // Move pahse to final review
        $this->actingAs($this->controllingEditor);

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->postJson(route('betterflow.v1.editorials.editorial.request-assist', [
            'editorial' => $this->editorial->getPublicId(),
        ]), [
            'title' => 'Final Review',
            'type' => EditorialTaskType::FinalReview,
            'description' => 'Please take a look at the final editorial and LMK your thoughts.',
            'user_id' => $this->gamuReporter->getKey(),
            'due_at' => Date::now()->addDays(2),
        ])->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::FinalReview,
            'user_id' => $this->gamuReporter->getKey(),
            'status' => TaskStatus::Pending
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->gamuReporter->getKey(),
            'type' => EditorialTaskType::FinalReview,
        ]);

        /**
         * Reporter: Gamu
         *
         * Logs in and
         * Does a final review, then completes the task
         */

        $this->actingAs($this->gamuReporter);

        $this->putJson(route('betterflow.v1.common.tasks.complete', [
            'task' => $this->gamuReporter->pendingTasks()->first()->getKey(),
        ]))->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::FinalReview,
            'user_id' => $this->gamuReporter->getKey(),
            'status' => TaskStatus::Completed
        ]);

        /** Controlling Editor
         *
         * Logs in and
         * completed their task for Final review
         */
        $this->actingAs($this->controllingEditor);

        $this->putJson(route('betterflow.v1.common.tasks.complete', [
            'task' => $this->controllingEditor->pendingTasks()->first()->getKey(),
        ]))->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('tasks', [
            'type' => EditorialTaskType::FinalReview,
            'user_id' => $this->controllingEditor->getKey(),
            'status' => TaskStatus::Completed
        ]);

        $this->assertEquals(EditorialPhase::Publishing, $this->editorial->fresh()->phase);
        $this->assertEquals(EditorialSubPhase::PublishingAssignment, $this->editorial->fresh()->sub_phase);

        $this->assertEmpty($this->editorial->refresh()->pendingTasks);

        $this->publishingWorkflow();
    }

    private function publishingWorkflow(): void
    {
        //
    }

    public static function workflowDataProvider(): array
    {
        // return an array of workflow names to run per test
        return [
            'needs_translation' => [
                [
                    'factCheckWorkflow',
                    'copyEditWorkflow',
                    'translationWorkflow',
                    // 'finalReviewWorkflow',
                ]
            ],
            'no_translation' => [
                [
                    'factCheckWorkflow',
                    'copyEditWorkflow',
                    // 'finalReviewWorkflow',
                ]
            ],
        ];
    }
}
