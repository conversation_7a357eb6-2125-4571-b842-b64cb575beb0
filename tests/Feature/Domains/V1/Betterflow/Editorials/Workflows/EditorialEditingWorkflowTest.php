<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Workflows;

use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Users\Models\Admin;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\Reporter;
use App\Domains\Users\Models\User;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Date;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class EditorialEditingWorkflowTest extends TestCase
{
    use FastRefreshDatabase;

    protected $controllingEditor;

    protected $toplineEditor;

    protected $reporter;

    protected $linda;

    protected $storyEditor;

    protected $photoEditor;

    protected $bennetResearcher;

    protected $kristaEditor;

    protected $mattEditor;

    protected $katieEditor;

    protected $editorial;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(RoleSeeder::class);
        $this->seed(PermissionsSeeder::class);

        // Setup all users and editorial
        // Setup users
        // Ndinda Kioko
        $this->toplineEditor = Editor::factory()->create([
            'name' => 'Terry Topline Editor',
        ]);

        $this->toplineEditor->assignRole('vertical_editor');
        $this->toplineEditor->givePermissionTo(Editor::getRolePermissions());

        $this->controllingEditor = Editor::factory()->create([
            'name' => 'Vertical Editor',
            'type' => 'editor',
        ]);

        $this->controllingEditor->assignRole('editor');
        $this->controllingEditor->givePermissionTo(Editor::getRolePermissions());

        // Gamu
        $this->reporter = Reporter::factory()->create([
            'name' => 'Gamu',
        ]);

        $this->reporter->givePermissionTo(Reporter::getRolePermissions());

        $this->linda = Reporter::factory()->create([
            'name' => 'Linda',
        ]);


        $this->linda->givePermissionTo(Reporter::getRolePermissions());

        // Story Edward
        $this->storyEditor = Editor::factory()->create([
            'type' => 'story_editor',
        ]);

        $this->storyEditor->givePermissionTo(Editor::getRolePermissions());

        // Charlotte Kesl
        $this->photoEditor = Editor::factory()->create([
            'type' => 'photo_editor',
        ]);

        $this->photoEditor->givePermissionTo(Editor::getRolePermissions());

        // Bennett
        $this->bennetResearcher = Editor::factory()->create([
            'type' => 'researcher',
        ]);

        $this->bennetResearcher->givePermissionTo(Editor::getRolePermissions());

        // Krista EIC
        $this->kristaEditor = Editor::factory()->create([
            'type' => 'admin',
        ]);

        $this->kristaEditor->givePermissionTo(Editor::getRolePermissions());;

        // Matt Haney
        $this->mattEditor = User::factory()->create([
            'type' => 'graphics_editor',
        ]);

        $this->mattEditor->givePermissionTo(Editor::getRolePermissions());

        // Katie
        $this->katieEditor = Admin::factory()->create([
            'type' => 'admin',
        ]);

        $this->katieEditor->givePermissionTo(Admin::getRolePermissions());

        // Create editorial
        $this->editorial = $this->reporter
            ->editorials()
            ->create(Editorial::factory([
                'assigned_to_id' => $this->controllingEditor,
                'phase' => EditorialPhase::Editing,
                'sub_phase' => EditorialSubPhase::EditingAssignment,
            ])
                ->make()
                ->toArray());
    }

    #[Test]
    #[DataProvider('languagesProvider')]
    public function it_runs_editorial_workflow_over_multiple_weeks(array $languages = []): void
    {
        // $this->withoutExceptionHandling();

        $schedule = app()->make(Schedule::class);

        /**
         * 1. **Ndinda Kioko** (Vertical Editor) has approved the pitch
         * *Why Specialists are Leaving Zimbabwe.” → by Reporter Gamuchirai Masiyiwa
         */
        Date::setTestNow(Date('2024-11-10'));

        // $queue = new PriorityQueue();

        /**
         * Workflows run in parallel time
         */
        $this->initialAssignments($languages);
        $this->travelTo(now()->addDays(1));
        $this->reportingWorkflow();
        $this->photoWorkflow();
        $this->illustrationWorkflow();      
    }

    protected function initialAssignments(array $languages): void
    {
        $this->editorial->tasks()->create([
            'user_id' => $this->controllingEditor->getKey(),
            'type' => EditorialTaskType::AssignEditors,
            'status' => TaskStatus::Pending,
        ]);

        // Handle assignments
        $response = $this->actingAs($this->controllingEditor)->putJson(route('betterflow.v1.editorials.assign', $this->editorial), [
            'topline_editor' => $this->toplineEditor->getKey(),
            'languages' => $languages,
            'users' => [
                [
                    'id' => $this->storyEditor->getKey(),
                    'role' => 'story_editor',
                    'asset' => 'reporting',
                ],
                [
                    'id' => $this->photoEditor->getKey(),
                    'role' => 'photo_editor',
                    'asset' => 'photo',
                ],
            ],
        ]);

        $this->editorial->refresh();

        // Check if editorial has multiple packages

        // Check if default package is created
        $this->assertDatabaseHas('editorial_packages', [
            'editorial_id' => $this->editorial->getKey(),
            'language_code' => 'en-EN',
        ]);

        $packageCount = count($languages) + 1;

        $this->assertCount($packageCount, $this->editorial->fresh()->packages);

        // Check if package for French is created
        if (in_array('fr-FR', $languages)) {
            $this->assertDatabaseHas('editorial_packages', [
                'editorial_id' => $this->editorial->getKey(),
                'language_code' => 'fr-FR',
            ]);
        }

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertEmpty($this->editorial->fresh()->pendingTasks);

        $this->assertCount(1, $this->editorial->assetOfType(AssetType::Reporting)->pendingTasks);
        $this->assertCount(1, $this->editorial->assetOfType(AssetType::Photo)->pendingTasks);

        $this->assertCount(1, $this->storyEditor->pendingTasks);
        $this->assertCount(1, $this->photoEditor->pendingTasks);

        $this->assertDatabaseHas('assets', [
            'editorial_id' => $this->editorial->getKey(),
            'type' => AssetType::Reporting,
            'assigned_to_id' => $this->storyEditor->getKey(),
        ]);

        $this->assertDatabaseHas('assets', [
            'editorial_id' => $this->editorial->getKey(),
            'type' => AssetType::Photo,
            'assigned_to_id' => $this->photoEditor->getKey(),
        ]);

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->controllingEditor->getKey(),
            'status' => TaskStatus::Completed,
            'type' => EditorialTaskType::AssignEditors,
        ]);
    }

    protected function reportingWorkflow(): void
    {
        /**
         * Charlotte Kesl (Photo Editor) logs in and creates an asset for Illustration
         */
        Date::setTestNow('2024-11-10');

        // Create asset
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.store', $this->editorial), [
            'assets' => [
                [
                    'type' => 'illustration',
                    'name' => 'Test Illustration',
                    'assignee' => $this->mattEditor->getKey(),
                ],
            ],
        ]);

        $response->assertCreated();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->actingAs($this->photoEditor)->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('assets', [
            'editorial_id' => $this->editorial->getKey(),
            'type' => AssetType::Illustration,
            'assigned_to_id' => $this->mattEditor->getKey(),
        ]);

        /**
         * First flow
         * Reporting asset
         */

        // Arrange.
        $reportingAsset = $this->editorial->fresh()->assetOfType(AssetType::Reporting);

        // Story Edward logs in and opens the editorial.
        $this->actingAs($this->storyEditor);

        // First assist request - Gamu, please fill out your reporting plan.
        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.request-assist',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
            ],
        ), [
            'user_id' => $this->reporter->getKey(),
            'title' => 'Reporting Plan',
            'description' => 'Gamu, please fill out your reporting plan.',
            'type' => EditorialTaskType::RequestedAssist,
            'due_at' => Date::createFromDate('2024-11-23'),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->storyEditor->getKey(),
        ]);

        // Story edward's task is on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => 'on_hold',
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => 'prepare_reporting_topline_edit',
            'notifiable_id' => $this->storyEditor->getKey(),
        ]);

        // Gamu's assist task.
        $assist1 = $this->reporter->pendingTasks()->latest()->first();

        // Travel a few days
        $this->travelTo(now()->addDays(2));

        // Gamu logs in and completes the first assist
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $assist1->getKey(),
            ],
        ));

        $response->assertOk();

        $this->actingAs($this->reporter)->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $assist1->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Story Edward's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Story Edward logs in and requests another assist
         * as the previous one was not satisfactory
         */
        $this->actingAs($this->storyEditor);

        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.request-assist',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
            ],
        ), [
            'user_id' => $this->reporter->getKey(),
            'title' => 'Reporting Plan',
            'description' => 'Gamu, your reporting plan was weak. Try again.',
            'type' => EditorialTaskType::RequestedAssist,
            'due_at' => Date::createFromDate('2024-11-25'),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();


        $assist2 = $this->reporter->pendingTasks()->latest()->first();

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'id' => $assist2->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->storyEditor->getKey(),
        ]);

        // Story Edward's task is on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => 'on_hold',
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => EditorialTaskType::RequestedAssist,
            'notifiable_id' => $this->reporter->getKey(),
        ]);

        /**
         * A day passes and
         * Gamu logs in and completes the second assist
         */
        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $assist2->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $assist2->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Story Edward's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        // Story Edward logs in and requests another assist
        $this->actingAs($this->storyEditor);

        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.request-assist',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
            ],
        ), [
            'title' => 'Draft',
            'description' => 'Gamu, please submit your first draft by next Thursday. 800 words',
            'user_id' => $this->reporter->getKey(),
            'due_at' => Date::createFromDate('2024-12-2'),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's most recent task
        $assist3 = $this->reporter->pendingTasks()->latest()->first();

        // Story Edward's task is on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-2'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->storyEditor->getKey(),
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => EditorialTaskType::RequestedAssist,
            'notifiable_id' => $this->reporter->getKey(),
        ]);

        /**
         * A few days pass and
         * Gamu misses her deadline
         */
        $this->travelTo(Date::createFromDate('2024-12-4'));

        $this->assertTrue(
            Date::createFromDate($assist3->due_at)->isPast(),
            'Task is not actually overdue',
        );

        $this->artisan('check:overdue-tasks')->assertSuccessful();

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => 'overdue_task',
        ]);

        // Gamu logs in and completes the third assist
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $assist3->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $assist3->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Story Edward's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => EditorialTaskType::RequestedAssist,
            'notifiable_id' => $this->reporter->getKey(),
        ]);

        /**
         * Story Edward logs in and requests another assist
         */
        $this->travelTo(Date::createFromDate('2024-12-2'));
        $this->actingAs($this->storyEditor);

        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.request-assist',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
            ],
        ), [
            'title' => 'Research',
            'type' => EditorialTaskType::RequestedAssist,
            'description' => 'Bennett, can you add in some data about the number of oncologists in Zimbabwe?',
            'user_id' => $this->bennetResearcher->getKey(),
            'due_at' => Date::createFromDate('2024-12-7'),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Bennett's most recent task
        $assist4 = $this->bennetResearcher->pendingTasks()->latest()->first();

        // Story Edward's task is on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Bennett's task is pending
        $this->assertDatabaseHas('tasks', [
            'id' => $assist4->getKey(),
            'user_id' => $this->bennetResearcher->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-7'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->storyEditor->getKey(),
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => EditorialTaskType::RequestedAssist,
            'notifiable_id' => $this->bennetResearcher->getKey(),
        ]);

        /**
         * Bennett logs in and completes the fourth assist
         */
        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->bennetResearcher);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $assist4->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Bennett's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $assist4->getKey(),
            'user_id' => $this->bennetResearcher->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Story Edward's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Story Edward logs in and requests another assist
         */
        $this->actingAs($this->storyEditor);

        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.request-assist',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
            ],
        ), [
            'title' => 'Revise',
            'type' => EditorialTaskType::RequestedAssist,
            'description' => 'Gamu, take a look at the latest draft and answer all of my comments and questions.',
            'user_id' => $this->reporter->getKey(),
            'due_at' => Date::createFromDate('2024-12-10'),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's most recent task
        $assist5 = $this->reporter->pendingTasks()->latest()->first();

        // Story Edward's task is on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'id' => $assist5->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-10'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->storyEditor->getKey(),
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => EditorialTaskType::RequestedAssist,
            'notifiable_id' => $this->reporter->getKey(),
        ]);

        /**
         * Gamu logs in and completes the fifth assist
         */
        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $assist5->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $assist5->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Story Edward's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Story Edward logs in and requests another assist
         */
        $this->actingAs($this->storyEditor);

        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.request-assist',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
            ],
        ), [
            'title' => 'Reporting Review',
            'type' => EditorialTaskType::RequestedAssist,
            'description' => 'Krista, can you take a look and LMK your thoughts?',
            'user_id' => $this->kristaEditor->getKey(),
            'due_at' => Date::createFromDate('2024-12-13'),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Bennett's most recent task
        $assist6 = $this->kristaEditor->pendingTasks()->latest()->first();

        // Story Edward's task is on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Bennett's task is pending
        $this->assertDatabaseHas('tasks', [
            'id' => $assist6->getKey(),
            'user_id' => $this->kristaEditor->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-13'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->storyEditor->getKey(),
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => EditorialTaskType::RequestedAssist,
            'notifiable_id' => $this->kristaEditor->getKey(),
        ]);

        /**
         * Krista logs in
         * and completes the sixth assist
         */
        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->kristaEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $assist6->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Krista's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $assist6->getKey(),
            'user_id' => $this->kristaEditor->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Story Edward's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Story Edward logs in and requests another assist
         */
        $this->actingAs($this->storyEditor);

        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.request-assist',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
            ],
        ), [
            'title' => 'Revise',
            'type' => EditorialTaskType::RequestedAssist,
            'description' => 'Gamu, we’re almost there. Please give the story one final read.',
            'user_id' => $this->reporter->getKey(),
            'due_at' => Date::createFromDate('2024-12-15'),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's most recent task
        $assist7 = $this->reporter->pendingTasks()->latest()->first();

        // Story Edward's task is on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'id' => $assist7->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-15'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->storyEditor->getKey(),
        ]);

        $this->assertDatabaseHas('notifications', [
            'type' => EditorialTaskType::RequestedAssist,
            'notifiable_id' => $this->reporter->getKey(),
        ]);

        /**
         * Gamu logs in and completes the seventh assist
         */
        $this->travelTo(now()->addDay());
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $assist7->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $assist7->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Story Edward's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Story Edward logs in
         * completes his initial task
         */
        $this->actingAs($this->storyEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $reportingAsset->type,
                'task' => $reportingAsset->ownerTask()->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Story Edward's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $reportingAsset->ownerTask()->getKey(),
            'user_id' => $this->storyEditor->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => 'prepare_reporting_topline_edit',
        ]);

        /**
         * Step 23 not yet implemented...
         */
        $this->assertEmpty($reportingAsset->pendingTasks);
    }

    protected function photoWorkflow(): void
    {
        /**
         * Second flow
         * Photo Asset
         * Task titles:** Plan; Photograph; Edit; Review; Revise; Caption
         * Tabs:** Plan; Photos; Captions; Comments; Activity Log; Research
         * Task Type:** photo_plan, photo_captions, photo_
         *
         * Responsible parties
         * Charlotte Kesl as Photo Editor
         * Gamu as Reporter
         * Linda Mujuru as Reporter/Audience Liaison
         * Katie as admin
         */
        Date::setTestNow(Date::createFromDate('2024-11-10'));

        $photoAsset = $this->editorial->assets()->where('type', 'photo')->first();

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
            'type' => 'prepare_photos_topline_edit',
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->photoEditor->getKey(),
            'type' => 'prepare_asset_for_topline_edit',
        ]);

        /**
         * Charlotte logs in and
         * assigns tasks
         */
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $photoAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Photo Plan',
            'description' => 'Gamu, please fill out your visual plan.',
            'due_at' => Date::createFromDate('2024-11-23'),
            'user_id' => $this->reporter->getKey(),
        ]);

        $visualPlanTask = $this->reporter->pendingTasks()->latest()->first();

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'id' => $visualPlanTask->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-11-23'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->photoEditor->getKey(),
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Gamu gets a notification
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Gamu logs in and
         * completes the first photo plan task
         */
        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $visualPlanTask->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $visualPlanTask->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Charlotte's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Charlotte logs in and
         * assigns tasks
         */
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $photoAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Photograph',
            'description' => 'Gamu, please upload your photos.',
            'due_at' => Date::createFromDate('2024-11-27'),
            'user_id' => $this->reporter->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-11-27'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->photoEditor->getKey(),
        ]);

        // Gamu is notified
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Gamu logs in and
         * completes the second photo task
         */
        $photoTask1 = $this->reporter->pendingTasks()->latest()->first();

        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $photoTask1->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $photoTask1->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Charlotte's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Charlotte logs in and
         * assigns tasks
         */
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $photoAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Photograph',
            'description' => "The hospital photos are all blurry. You'll need to reshoot.",
            'due_at' => Date::createFromDate('2024-11-30'),
            'user_id' => $this->reporter->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-11-30'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->photoEditor->getKey(),
        ]);

        // Gamu is notified
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Gamu logs in and
         * reshoots her photos
         * and completes the third photo task
         */
        $photoTask2 = $this->reporter->pendingTasks()->latest()->first();

        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $photoTask2->getKey(),
            ],
        ), [
            'notes' => "Charlotte, I don't think these photos will work either. I spoke with Linda, who can help us out by taking photos in her area.",
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $photoTask2->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Charlotte's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Charlotte logs in and
         * assigns tasks
         */
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $photoAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Photograph',
            'description' => 'Take photos of the hospital in your area.',
            'due_at' => Date::createFromDate('2024-12-04'),
            'user_id' => $this->linda->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Linda's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->linda->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-04'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->photoEditor->getKey(),
        ]);

        // Linda is notified
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->linda->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Linda takes photos and
         * logs in and
         * checks off the her photo task
         */
        $photoTask3 = $this->linda->pendingTasks()->latest()->first();

        $this->travelTo(now()->addDays(2));
        $this->actingAs($this->linda);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $photoTask3->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Linda's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $photoTask3->getKey(),
            'user_id' => $this->linda->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Charlotte's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Charlotte logs in and
         * assigns tasks
         */
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $photoAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Photo Caption',
            'description' => 'Fill out the story captions for the selected images.',
            'due_at' => Date::createFromDate('2024-12-06'),
            'user_id' => $this->reporter->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-06'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->photoEditor->getKey(),
        ]);

        // Gamu is notified
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Gamu logs in and
         * completes the caption task
         */
        $photoTask4 = $this->reporter->pendingTasks()->latest()->first();

        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $photoTask4->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $photoTask4->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Charlotte's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Charlotte logs in and
         * assigns tasks
         */
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $photoAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Photo Review',
            'description' => 'Katie, can you take a look and LMK what you think?',
            'due_at' => Date::createFromDate('2024-12-07'),
            'user_id' => $this->katieEditor->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Katie's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->katieEditor->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-07'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->photoEditor->getKey(),
        ]);

        // Katie is notified
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->katieEditor->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Katie logs in and
         * completes the review task
         */
        $photoTask5 = $this->katieEditor->pendingTasks()->latest()->first();

        $this->actingAs($this->katieEditor);

        /**
         * Katie adds some comments to the photo asset
         */
        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.comments.store',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
            ],
        ), [
            'comment' => 'Test comment.',
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('comments', [
            'commentable_id' => $photoAsset->getKey(),
            'commentable_type' => 'asset',
        ]);

        /**
         * Katie checks off her review task
         */
        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $photoTask5->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Katie's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $photoTask5->getKey(),
            'user_id' => $this->katieEditor->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Charlotte's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Gamu logs in and
         * adds a comment to the photo asset
         */
        $this->actingAs($this->reporter);

        $response = $this->postJson(route(
            'betterflow.v1.editorials.assets.comments.store',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
            ],
        ), [
            'comment' => 'I was able to take some additional photos of the hospital.',
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        $this->assertDatabaseHas('comments', [
            'commentable_id' => $photoAsset->getKey(),
            'commentable_type' => 'asset',
        ]);

        /**
         * Step 35 not implemented
         * Charlotte unchecks the “Asset Complete” box
         */

        /**
         * Step 36
         * Charlotte logs in and
         * assigns a new task
         */
        $this->actingAs($this->photoEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $photoAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Photograph',
            'description' => 'Gamu, please add your additional hospital photos.',
            'due_at' => Date::createFromDate('2024-12-08'),
            'user_id' => $this->reporter->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-08'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->photoEditor->getKey(),
        ]);

        // Gamu is notified
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Gamu logs in
         * 37. uploads some images (not implemented)
         * 38. completes the task
         */
        $photoTask6 = $this->reporter->pendingTasks()->latest()->first();

        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $photoTask6->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $photoTask6->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Charlotte's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Charlotte logs in and
         * completes her initial task
         */
        $this->actingAs($this->photoEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $photoAsset->type,
                'task' => $photoAsset->ownerTask()->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Charlotte's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $photoAsset->ownerTask()->getKey(),
            'user_id' => $this->photoEditor->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => 'prepare_photos_topline_edit',
        ]);

        /**
         * Step 40
         * Charlotte logs in and
         * marks asset complete (not implemented)
         */
        $this->assertEmpty($photoAsset->pendingTasks);
    }

    protected function illustrationWorkflow(): void
    {
        /**
         * Flow 3
         * Illustration asset
         *
         * Task titles: Plan; Draw; Review; Revise
         * Tabs: Plan; Sketches; Final Illustration/s; Comments; Activity Log; Research
         *
         * Responsible parties
         * Matt Haney - Illustrator
         * Charlotte Kesl - Photo Editor
         * Gamu Masiyiwa - Reporter
         */
        Date::setTestNow(Date::createFromDate('2024-11-10'));

        $illustrationAsset = $this->editorial->assets()->where('type', 'illustration')->first();
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Step 3
         * Matt logs in and
         * assigns a new task
         */
        $this->actingAs($this->mattEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $illustrationAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Draw',
            'description' => 'Sketches',
            'due_at' => Date::createFromDate('2024-11-25'),
            'user_id' => $this->mattEditor->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Matt's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Matts's new task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-11-25'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->mattEditor->getKey(),
        ]);

        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->mattEditor->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * 5
         * Matt logs in and
         * completes the task
         */
        $illustrationTask1 = $this->mattEditor->pendingTasks()->latest()->first();

        $this->actingAs($this->mattEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $illustrationAsset->type,
                'task' => $illustrationTask1->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Matt's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $illustrationTask1->getKey(),
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Matt's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Step 7
         * Matt logs in and
         * assigns a new task
         * Missing assignee and description
         */
        $this->actingAs($this->mattEditor);

        /**
         * Charlotte logs in and
         * checks off her own task?
         */

        /**
         * Matt logs in and
         * assigns a new task
         */
        $this->actingAs($this->mattEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $illustrationAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Draw',
            'description' => 'Final illos',
            'due_at' => Date::createFromDate('2024-12-02'),
            'user_id' => $this->mattEditor->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Matt's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Matts's new task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-12-02'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->mattEditor->getKey(),
        ]);


        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->mattEditor->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);
        /**
         * Matt logs in and
         * completes the task
         */
        $illustrationTask2 = $this->mattEditor->pendingTasks()->latest()->first();

        $this->actingAs($this->mattEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $illustrationAsset->type,
                'task' => $illustrationTask2->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Matt's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $illustrationTask2->getKey(),
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Matt's preparations task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Matt logs in and
         * assigns a new task
         */
        $this->actingAs($this->mattEditor);

        $response = $this->postJson(route('betterflow.v1.editorials.assets.assign-task', [
            'editorial' => $this->editorial->getPublicId(),
            'asset' => $illustrationAsset->type,
        ]), [
            'type' => EditorialTaskType::RequestedAssist,
            'title' => 'Review',
            'description' => 'Gamu, does this work better?',
            'due_at' => Date::createFromDate('2024-11-29'),
            'user_id' => $this->reporter->getKey(),
        ]);

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Matt's task is now on hold
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::OnHold,
        ]);

        // Gamu's new task is pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Pending,
            'due_at' => Date::createFromDate('2024-11-29'),
            'type' => EditorialTaskType::RequestedAssist,
            'requested_by_id' => $this->mattEditor->getKey(),
        ]);

        // Gamu is notified
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->reporter->getKey(),
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        /**
         * Gamu logs in and
         * completes the task
         */
        $illustrationTask3 = $this->reporter->pendingTasks()->latest()->first();

        $this->actingAs($this->reporter);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $illustrationAsset->type,
                'task' => $illustrationTask3->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Gamu's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $illustrationTask3->getKey(),
            'user_id' => $this->reporter->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => EditorialTaskType::RequestedAssist,
        ]);

        // Gamu's task is back to pending
        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Pending,
        ]);

        /**
         * Matt logs in and
         * checks off his own task
         */
        $this->actingAs($this->mattEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => $illustrationAsset->type,
                'task' => $illustrationAsset->ownerTask()->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))->assertOk();

        // Matt's task is completed
        $this->assertDatabaseHas('tasks', [
            'id' => $illustrationAsset->ownerTask()->getKey(),
            'user_id' => $this->mattEditor->getKey(),
            'status' => TaskStatus::Completed,
            'progress' => 100,
            'type' => 'prepare_illustrations_topline_edit',
        ]);

        $this->assertEmpty($illustrationAsset->pendingTasks);
    }

    protected function toplineEditReview()
    {
        echo 'Topline Edit Review' . PHP_EOL;

        $this->editorial->assets()->each(function ($asset) {
            $this->assertTrue($asset->isComplete());
        });

        $this->assertEquals(EditorialSubPhase::ToplineEdit, $this->editorial->fresh()->sub_phase);

        $this->actingAs($this->toplineEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.tasks.incomplete',
            [
                'task' => $this->mattEditor->completedTasks()->first()->getKey(),
                'notes' => 'I need you to make some changes',
            ],
        ));

        $this->assertDatabaseHas('assets', [
            'type' => 'illustration',
            'completed_at' => null,
        ]);

        $this->putJson(route('betterflow.v1.editorials.tasks.complete', [
            'task' => $this->toplineEditor->pendingTasks()->first()->getKey(),
        ]));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertOk();

        $this->assertEquals(EditorialSubPhase::Editing, $this->editorial->fresh()->sub_phase);

        /**
         *  Then Matt works on his stuff and
         *  completes his tasks
         */

        $this->actingAs($this->mattEditor);

        $response = $this->putJson(route(
            'betterflow.v1.editorials.assets.tasks.complete',
            [
                'editorial' => $this->editorial->getPublicId(),
                'asset' => 'illustration',
                'task' => $this->mattEditor->completedTasks()->first()->getKey(),
            ],
        ));

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $this->editorial))
            ->assertOk();

        $this->assertEquals(EditorialSubPhase::ToplineEdit, $this->editorial->fresh()->sub_phase);

        $this->assertDatabaseHas('tasks', [
            'user_id' => $this->toplineEditor->getKey(),
            'status' => TaskStatus::Pending,
            'type' => EditorialTaskType::ToplineEdit
        ]);
    }

    public static function languagesProvider(): array
    {
        return [
            'one_langauge' => [
                [],
            ],
            'multiple_langauges' => [
                ['fr-FR'],
            ]
        ];
    }
}
