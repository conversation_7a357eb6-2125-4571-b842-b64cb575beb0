<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\Editor;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class MoveEditorialPhaseTest extends TestCase
{
    use FastRefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    #[Test]
    public function it_can_move_editorial_editing_phase(): void
    {
        $editor = Editor::factory()->create([
            'type' => 'vertical_editor',
        ]);
        
        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $editor->getKey(),
            'phase' => EditorialPhase::Editing,
            'sub_phase' => EditorialSubPhase::EditingAssignment,
        ]);
        $editor->assignRole('vertical_editor');

        $this->actingAs($editor);

        $response = $this->putJson(
            route('betterflow.v1.editorials.move-phase', [
                'editorial' => $editorial->getPublicKey(),
            ]),
            [
                'phase' => EditorialPhase::Production,
                'sub_phase' => EditorialSubPhase::FactCheckAssignment,
            ],
        );

        $response->assertOk();
        
        $this->getJson(route('betterflow.v1.editorials.show', $editorial))->assertOk();
        
        $this->assertDatabaseHas('editorials', [
            'id' => $editorial->getKey(),
            'phase' => EditorialPhase::Production,
            'sub_phase' => EditorialSubPhase::FactCheckAssignment,
        ]);
    }

    #[Test]
    public function it_can_move_editorial_phase(): void
    {
        
        $editor = Editor::factory()->create([
            'type' => 'vertical_editor',
        ]);
        
        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $editor->getKey(),
            'phase' => EditorialPhase::Production,
            'sub_phase' => EditorialSubPhase::FactCheckAssignment,
        ]);
        $editor->assignRole('vertical_editor');

        $this->actingAs($editor);

        $response = $this->putJson(
            route('betterflow.v1.editorials.move-phase', [
                'editorial' => $editorial->getPublicKey(),
            ]),
            [
                'phase' => EditorialPhase::Production,
                'sub_phase' => EditorialSubPhase::CopyEdit,
            ],
        );

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $editorial))->assertOk();

        $this->assertDatabaseHas('editorials', [
            'id' => $editorial->getKey(),
            'phase' => EditorialPhase::Production,
            'sub_phase' => EditorialSubPhase::CopyEdit,
        ]);
    }

    #[Test]
    public function it_can_move_editorial_phase_publish(): void
    {
        $editor = Editor::factory()->create([
            'type' => 'vertical_editor',
        ]);

        $editorial = Editorial::factory()->create([
            'phase' => EditorialPhase::Production,
            'sub_phase' => EditorialSubPhase::CopyEdit,
            'assigned_to_id' => $editor->getKey(),
        ]);

        $editor->assignRole('vertical_editor');

        $this->actingAs($editor);

        $response = $this->putJson(
            route('betterflow.v1.editorials.move-phase', [
                'editorial' => $editorial->getPublicKey(),
            ]),
            [
                'phase' => EditorialPhase::Publishing,
            ],
        );

        $response->assertOk();

        $this->getJson(route('betterflow.v1.editorials.show', $editorial))->assertOk();

        $editorial->refresh();

        $this->assertEquals($editor->getKey(), $editorial->assigned_to_id);

        $this->assertEquals($editor->getKey(), $editorial->currentOwner()->getKey());

        $this->assertEquals('vertical_editor', $editorial->currentOwner()->type);

        $this->assertEquals(EditorialPhase::Publishing, $editorial->phase);

        $this->assertDatabaseHas('editorials', [
            'id' => $editorial->getKey(),
            'phase' => EditorialPhase::Publishing,
            'sub_phase' => EditorialSubPhase::PublishingAssignment,
        ]);
    }
}
