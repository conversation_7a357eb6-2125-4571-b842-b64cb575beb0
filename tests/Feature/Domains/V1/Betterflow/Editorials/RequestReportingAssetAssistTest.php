<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Http\Resources\Permissions;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\User;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use Database\Seeders\Roles\EditorSeeder;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class RequestReportingAssetAssistTest extends TestCase
{
    use FastRefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(PermissionsSeeder::class);
        $this->seed(RoleSeeder::class);
        $this->seed(EditorSeeder::class);
    }

    #[Test]
    public function it_can_request_an_assist_for_a_reporting_asset(): void
    {
        
        // Arrange
        $user = User::factory()->create();
        $editor = Editor::factory()->create([
            'job_title' => 'story editor',
            'type' => 'story_editor',

        ]);

        $storyEditorRole = Role::findOrCreate('story_editor', 'api');
        $storyEditorRole->givePermissionTo(Permission::REQUEST_EDITORIAL_ASSIST);

        $editor->assignRole('story_editor');

        $editorial = Editorial::factory()->create();

        $asset = $editorial->assets()->create([
            'assigned_to_id' => $editor->getKey(),
            'type' => 'reporting',
        ]);

        $asset->tasks()->create([
            'user_id' => $editor->getKey(),
            'title' => 'Prepare reporting plan',
            'type' => 'reporting_plan',
            'due_at' => now()->addWeek(),
        ]);

        $this->actingAs($editor);

        $assistData = [
            'type' => 'reporting_plan_assist',
            'title' => 'Assist Request for Reporting Asset',
            'description' => 'Need help with the reporting asset',
            'user_id' => $user->id,
            'due_at' => now()->addDays(7),
            'instructions' => 'Please review and provide feedback',
        ];

        // Act
        $response = $this->postJson(route('betterflow.v1.editorials.assets.request-assist', [
            'editorial' => $editorial->getPublicId(),
            'asset' => $asset->type,
        ]), $assistData);

        // Assert
        $response->assertJson([
            'success' => true,
            'message' => 'Assistance requested.',
        ]);

        // Verify task creation
        $this->assertDatabaseHas('tasks', [
            'user_id' => $user->getKey(),
            'requested_by_id' => $editor->getKey(),
            'title' => $assistData['title'],
            'description' => $assistData['description'],
            'type' => $assistData['type'],
        ]);
    }

    #[Test]
    public function it_requires_valid_data_to_request_an_assist(): void
    {
        // Arrange
        $user = User::factory()->create();
        $editorial = Editorial::factory()->create();
        $asset = Asset::factory()->for($editorial)->create([
            'type' => 'reporting',
        ]);


        $role = Role::findOrCreate('reporter', 'api');
        $role->givePermissionTo([Permission::REQUEST_EDITORIAL_ASSIST, Permission::REQUEST_ASSET_ASSIST]);

        $user->assignRole($role);
        $this->actingAs($user);


        $invalidData = [
            // Missing required fields
            'type' => '',
            'title' => '',
            'description' => '',
            'user_id' => null,
        ];

        // Act & Assert
        $response = $this->postJson(route('betterflow.v1.editorials.assets.request-assist', [
            'editorial' => $editorial->getPublicId(),
            'asset' => $asset->type,
        ]), $invalidData);

       
        $response->assertInvalid('type');
        $response->assertJsonValidationErrors([
            'type', 'title', 'description', 'user_id',
        ]);
    }

    #[Test]
    public function it_handles_optional_fields_when_requesting_an_assist(): void
    {
        // Arrange
        $user = User::factory()->create();
        $editor = Editor::factory()->create([
            'job_title' => 'Story editor',
            'type' => 'story_editor',
        ]);

        $role = Role::findOrCreate('story_editor', 'api');
        $role->givePermissionTo([Permission::REQUEST_ASSET_ASSIST, Permission::REQUEST_EDITORIAL_ASSIST]);
        $editor->assignRole($role);

        $editorial = Editorial::factory()->create();

        $asset = $editorial->assets()->create([
            'assigned_to_id' => $editor->getKey(),
            'type' => 'reporting',
        ]);

        $asset->tasks()->create([
            'user_id' => $editor->getKey(),
            'title' => 'Prepare reporting plan',
            'type' => 'reporting_plan',
            'due_at' => now()->addWeek(),
        ]);

        $this->actingAs($editor);

        $assistData = [
            'type' => 'reporting_assist',
            'title' => 'Assist Request with Optional Fields',
            'description' => 'Minimal assist request',
            'user_id' => $user->id,
            // Omitting optional fields
        ];

        // Act
        $response = $this->postJson(route('betterflow.v1.editorials.assets.request-assist', [
            'editorial' => $editorial->getPublicId(),
            'asset' => $asset->type,
        ]), $assistData);

        // Assert
        $response->assertJson([
            'success' => true,
            'message' => 'Assistance requested.',
        ]);

        $this->assertDatabaseHas('tasks', [
            'user_id' => $user->id,
            'title' => $assistData['title'],
            'description' => $assistData['description'],
            'type' => $assistData['type'],
        ]);
    }
}
