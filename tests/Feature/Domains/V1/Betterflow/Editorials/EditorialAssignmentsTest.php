<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials;

use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\User;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use Illuminate\Contracts\Auth\Authenticatable;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class EditorialAssignmentsTest extends TestCase
{
    use FastRefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(PermissionsSeeder::class);
        $this->seed(RoleSeeder::class);
    }

    #[Test]
    public function it_can_assign_single_user_to_editorial(): void
    {
        $user = User::factory()->create();
        $toplineEditor = Editor::factory()->create();
        /** @var Authenticatable|User $verticalEditor */
        $verticalEditor = Editor::factory()->create([
            'type' => 'vertical_editor',
        ]);
        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $verticalEditor
        ]);

        $verticalEditor->assignRole('vertical_editor');
        $verticalEditor->givePermissionTo('assign users');

        $response = $this->actingAs($verticalEditor)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    'id' => $user->getKey(),
                    'role' => 'web_editor',
                    'asset' => AssetType::Reporting,
                ],
            ],
        ]);

        $response->assertOk();

        $this->assertDatabaseHas('assignments', [
            'assignable_id' => $editorial->getKey(),
            'user_id' => $user->getKey(),
            'role' => 'web_editor',
        ]);

        $this->assertDatabaseHas('assets', [
            'editorial_id' => $editorial->getKey(),
            'type' => AssetType::Reporting,
            'assigned_to_id' => $user->getKey(),
        ]);
    }

    #[Test]
    public function it_cannot_assign_multiple_assets_of_same_type(): void
    {
        $user = User::factory()->create();
        $user2 = User::factory()->create();
        /** @var Authenticatable|User $verticalEditor */
        $verticalEditor = Editor::first() ?? Editor::factory()->create();
        $verticalEditor->assignRole('vertical_editor');
        $verticalEditor->givePermissionTo('assign users');

        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $verticalEditor
        ]);

        // First assignment should succeed
        $firstResponse = $this->actingAs($verticalEditor)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    'id' => $user->getKey(),
                    'role' => 'web_editor',
                    'asset' => AssetType::Reporting,
                ],
            ],
        ]);

        $firstResponse->assertOk();

        // Second assignment of same asset type should fail
        $secondResponse = $this->actingAs($verticalEditor)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    'id' => $user2->getKey(),
                    'role' => 'story_editor',
                    'asset' => AssetType::Reporting,
                ],
            ],
        ]);

        $secondResponse->assertConflict();
    }

    #[Test]
    public function it_can_assign_multiple_users_with_different_roles(): void
    {
        $photoEditor = Editor::factory()->create([
            'email' => '<EMAIL>'
        ]);
        $storyEditor = Editor::factory()->create([
            'email' => '<EMAIL>'
        ]);
        $writer = User::factory()->create();
        /** @var Authenticatable|User $verticalEditor */
        $verticalEditor = Editor::first();
        $verticalEditor->assignRole('vertical_editor');
        $verticalEditor->givePermissionTo('assign users');


        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $verticalEditor
        ]);
        $resource = EditorialResource::make($editorial);

        $response = $this->actingAs($verticalEditor)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    'id' => $photoEditor->getKey(),
                    'role' => 'photo_editor',
                    'asset' => AssetType::Photo,
                ],
                [
                    'id' => $storyEditor->getKey(),
                    'role' => 'story_editor',
                    'asset' => AssetType::Reporting,
                ],
            ],
        ]);

        $response->assertOk();

        // TODO: Try to assert the response data
        $response->assertJsonIsObject();
        // $response->assertJsonFragment(['watcher_ids' => []]);

        $this->assertDatabaseHas('assets', [
            'editorial_id' => $editorial->getKey(),
            'type' => AssetType::Photo,
            'assigned_to_id' => $photoEditor->getKey(),
        ]);

        $this->assertDatabaseHas('assets', [
            'editorial_id' => $editorial->getKey(),
            'type' => AssetType::Reporting,
            'assigned_to_id' => $storyEditor->getKey(),
        ]);

        $this->assertDatabaseHas('assignments', [
            'assignable_id' => $editorial->getKey(),
            'assignable_type' => 'editorial',
            'user_id' => $storyEditor->getKey(),
            'role' => 'story_editor',
        ]);

        $this->assertDatabaseHas('assignments', [
            'assignable_id' => $editorial->getKey(),
            'assignable_type' => 'editorial',
            'user_id' => $photoEditor->getKey(),
            'role' => 'photo_editor',
        ]);
    }

    #[Test]
    public function it_cannot_assign_user_without_permission(): void
    {
        /** @var Authenticatable|User $unauthorizedUser */
        $unauthorizedUser = User::factory()->create();
        $editorial = Editorial::factory()->create();

        $response = $this->actingAs($unauthorizedUser)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    'id' => $unauthorizedUser->id,
                    'role' => 'writer',
                    'asset' => 'draft',
                ],
            ],
        ]);

        $response->assertForbidden();
    }

    #[Test]
    public function it_can_assign_topline_editor(): void
    {
        $reportingEditor = Editor::factory()->create([
            'email' => '<EMAIL>'
        ]);

        /** @var Authenticatable|User $verticalEditor */
        $verticalEditor = Editor::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $verticalEditor
        ]);
        $toplineEditor = User::factory()->create();

        $verticalEditor->assignRole('vertical_editor');
        $verticalEditor->givePermissionTo('assign users');

        $response = $this->actingAs($verticalEditor)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    'id' => $toplineEditor->getKey(),
                    'role' => 'topline_editor',
                ],
                [
                    
                    'id' => $reportingEditor->getKey(),
                    'role' => 'story_editor',
                    'asset' => AssetType::Reporting,
                ],
            ],
            'topline_editor' => $toplineEditor->getKey(),
        ]);

        $response->assertOk();
        $this->assertEquals($toplineEditor->id, $editorial->fresh()->topline_editor_id);

        $this->assertDatabaseHas('editorials', [
            'id' => $editorial->getKey(),
            'topline_editor_id' => $toplineEditor->getKey(),
        ]);
    }

    #[Test]
    public function validation_fails_for_invalid_user_data(): void
    {
        /** @var Authenticatable|User $verticalEditor */
        $verticalEditor = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $verticalEditor->assignRole('vertical_editor');
        $verticalEditor->givePermissionTo('assign users');

        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $verticalEditor
        ]);

        $response = $this->actingAs($verticalEditor)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    // Missing required fields
                ],
            ],
        ]);

        $response->assertUnprocessable();
        $response->assertJsonValidationErrors([
            'users.0.id',
        ]);
    }

    #[Test]
    #[DataProvider('languagesProvider')]
    public function if_languages_passed_it_creates_packages_for_each_language(array $languages): void
    {
        /** @var Authenticatable|User $verticalEditor */
        $verticalEditor = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        $verticalEditor->assignRole('vertical_editor');
        $verticalEditor->givePermissionTo('assign users');

        $editorial = Editorial::factory()->create([
            'assigned_to_id' => $verticalEditor
        ]);

        $response = $this->actingAs($verticalEditor)->putJson(route('betterflow.v1.editorials.assign', $editorial), [
            'users' => [
                [
                    'id' => $verticalEditor->getKey(),
                    'role' => 'vertical_editor',
                ],
            ],
            'languages' => $languages,
        ]);

        $response->assertOk();

        // Assert editorial packages has default language
        $this->assertDatabaseHas('editorial_packages', [
            'editorial_id' => $editorial->getKey(),
            'language_code' => 'en-EN',
        ]);

        foreach ($languages as $language) {
            $this->assertDatabaseHas('editorial_packages', [
                'editorial_id' => $editorial->getKey(),
                'language_code' => $language,
            ]);
        }
    }

    public static function languagesProvider(): array
    {
        return [
            'one_langauge' => [
                ['fr-FR'],
            ],
            'multiple_langauges' => [
                ['fr-FR', 'es-ES'],
            ]
        ];
    }
}
