<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Assets;

use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Reporting;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class AssetsControllerTest extends TestCase
{
    use FastRefreshDatabase;

    private User $user;

    private Editorial $editorial;

    private Asset $asset;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->editorial = Editorial::factory()->create();
        $this->asset = Reporting::factory()->create([
            'editorial_id' => $this->editorial->id,
            'type' => AssetType::Reporting,
        ]);

        $this->asset->content()->create([
            'language_code' => 'en-EN',
            'data' => [],
        ]);
    }

    #[Test]
    public function it_can_list_assets_for_an_editorial(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.index', [
                'editorial' => $this->editorial->getPublicKey(),
            ]));

        $response->assertOk()
            ->assertJsonStructure(
                [
                    '*' => [
                        'id',
                        'name',
                        'type',
                        'url',
                        'required',
                        'description',
                        'content',
                    ],
                ],
            );
    }

    #[Test]
    public function it_can_show_a_specific_asset(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.show', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->type,
            ]));

        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'name',
                'slug',
                'url',
                'required',
                'type',
                'schema_version',
                'assignee',
                'content',
            ]);
    }

    #[Test]
    public function it_can_update_an_asset(): void
    {
        $updateData = [
            'language' => 'en-EN',
            'data' => [
                'title' => 'Updated title',
            ],
        ];

        $response = $this->actingAs($this->user)
            ->putJson(
                route('betterflow.v1.editorials.assets.update', [
                    'editorial' => $this->editorial->getPublicKey(),
                    'asset' => $this->asset->type,
                ], $updateData),
            );

        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'name',
                'slug',
                'type',
                'assignee',
                'content',
            ]);

        $this->assertDatabaseHas('assets', [
            'id' => $this->asset->getKey(),
            // 'type' => $updateData['type'],
        ]);

        $this->assertDatabaseHas('asset_contents', [
            'language_code' => $updateData['language'],
            // 'data' => $updateData['data']
        ]);
    }
}
