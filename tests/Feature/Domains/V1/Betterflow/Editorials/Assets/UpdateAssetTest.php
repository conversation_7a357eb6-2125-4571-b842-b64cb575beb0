<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Assets;

use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\Editor;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UpdateAssetTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(RoleSeeder::class);
        $this->seed(PermissionsSeeder::class);
    }

    #[Test]
    public function it_can_mark_an_asset_complete(): void
    {
        // $this->markTestIncomplete();
        $editor = Editor::factory()->create([
            'type' => 'vertical_editor',
        ]);
        $toplineEditor = Editor::factory()->create([
            'type' => 'managing_editor',
        ]);

        $editorial = Editorial::factory()->create([
            'topline_editor_id' => $toplineEditor->getKey(),
        ]);

        $editorial->assets()->create([
            'type' => 'reporting',
            'assigned_to_id' => $editor->getKey(),
        ]);

        $asset = $editorial->assetOfType(AssetType::Reporting);

        $asset->tasks()->create([
            'user_id' => $editor->getKey(),
            'type' => 'reporting_plan',
        ]);

        $task = $asset->tasks->first();

        $editor->assignRole('vertical_editor');

        $this->actingAs($editor);

        $response = $this->putJson(route('betterflow.v1.editorials.assets.tasks.complete', [
            'editorial' => $editorial->getPublicId(),
            'asset' => AssetType::Reporting->value,
            'task' => $task->getKey(),
        ]), [
            'notes' => 'Task completed',
        ]);

        $response->assertOk();

        $this->assertDatabaseHas('assets', [
            'id' => $asset->getKey(),
        ]);

        $this->assertEquals(now()->format('Y-m-d h'), $editorial->assetOfType(AssetType::Reporting)->completed_at->format('Y-m-d h'));

        $this->assertTrue($asset->fresh()->isComplete());
        $this->assertFalse($asset->fresh()->isPending());
    }

    #[Test]
    public function it_can_mark_an_asset_not_complete(): void
    {
        $asset = Asset::factory()->create();

        $editor = Editor::factory()->create([
            'type' => 'vertical_editor',
        ]);

        $editor->assignRole('vertical_editor');

        $this->actingAs($editor);

        $response = $this->putJson(
            route('betterflow.v1.editorials.assets.update', [
                'editorial' => $asset->editorial->getPublicKey(),
                'asset' => $asset->slug,
            ]),
            [
                'completed' => false,
            ],
        );

        $response->assertOk();

        $this->assertDatabaseHas('assets', [
            'id' => $asset->getKey(),
            'completed_at' => null,
        ]);

        $this->assertFalse($asset->fresh()->isComplete());
        $this->assertTrue($asset->fresh()->isPending());
    }

    #[Test]
    public function if_not_completed_added_to_request_asset_is_not_updated(): void
    {
        $asset = Asset::factory()->create();

        $editor = Editor::factory()->create([
            'type' => 'vertical_editor',
        ]);

        $editor->assignRole('vertical_editor');

        $this->actingAs($editor);

        $response = $this->putJson(
            route('betterflow.v1.editorials.assets.update', [
                'editorial' => $asset->editorial->getPublicKey(),
                'asset' => $asset->slug,
            ]),
        );

        $response->assertOk();

        $this->assertDatabaseHas('assets', [
            'id' => $asset->getKey(),
            'completed_at' => null,
        ]);

        $this->assertFalse($asset->fresh()->isComplete());
        $this->assertTrue($asset->fresh()->isPending());
    }
}
