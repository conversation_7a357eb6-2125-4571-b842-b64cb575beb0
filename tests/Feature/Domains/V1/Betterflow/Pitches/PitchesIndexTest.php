<?php

namespace Tests\Feature\Domains\V1\Betterflow\Pitches;

use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Users\Models\Reporter;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PitchesIndexTest extends TestCase
{
    #[Test]
    public function it_gets_pitches_grouped(): void
    {
        $pitches = collect();

        $reporter = Reporter::query()->first() ?? Reporter::factory()->create();

        foreach (range(1, 10) as $_key) {
            $createdBy = $_key % 2 == 0 ? $reporter : null;
            $withStates = $_key % 2 == 0 ? false : true;

            $pitches->push($this->createPitch([], $createdBy, $withStates));
        }

        $states = $pitches->map(function (Pitch $pitch) {
            return $pitch->fresh()->currentStateId->value;
        });

        $states->filter(fn(string $state): bool => $state === PitchStage::Submitted->value)->count();

        $this->actingAs($reporter)
            ->getJson(route('betterflow.v1.pitches.index', [
                'grouped' => 1,
            ]))
            ->assertOk();
        // $response->assertJson(function(AssertableJson $json) use ($submittedCount) {

        //     $json->has(PitchStage::Submitted->value);

        // });
    }

    #[Test]
    public function it_gets_pitches_grouped_with_drafts(): void
    {
        $pitches = collect();

        $reporter = Reporter::query()->first() ?? Reporter::factory()->create();

        foreach (range(1, 10) as $_key) {
            $createdBy = $_key % 2 == 0 ? $reporter : null;
            $withStates = $_key % 2 == 0 ? false : true;

            $pitches->push($this->createPitch([], $createdBy, $withStates));
        }

        $states = $pitches->map(function (Pitch $pitch) {
            return $pitch->fresh()->currentStateId->value;
        });

        $states->filter(fn(string $state): bool => $state === PitchStage::Submitted->value)->count();

        $this->actingAs($reporter)
            ->getJson(route('betterflow.v1.pitches.index', [
                'grouped' => 1,
                'drafts' => 1,
            ]))
            ->assertOk();
        // $response->assertJson(function(AssertableJson $json) use ($submittedCount) {
        //     $json->has(PitchStage::Submitted->value);
        //     $json->has(PitchStage::Draft->value);
        // });
    }
}
