<?php

namespace Tests\Feature\Domains\V1\Betterflow\Pitches;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\Reporter;
use App\Domains\Verticals\Models\Vertical;
use Database\Seeders\Crud\PitchTypes;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class PitchCrudTest extends TestCase
{
    use FastRefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        if (PitchType::all()->isEmpty()) {
            $this->seed(PitchTypes::class);
        }
    }

    #[Test]
    public function authenticated_users_with_permissions_can_create_a_pitch(): void
    {
        // $this->withoutExceptionHandling();
        Pitch::truncate();
        Storage::fake('r2');
        /** @var Reporter $reporter */
        $reporter = Reporter::factory()->verified()->create();
        $topic = Topic::factory()->create();
        $pitch = Pitch::factory()->make([
            'slug' => 'test-123',
            'created_by' => $reporter->getKey(),
            'topic' => $topic->getKey(),
            'pitch_type' => fake()->randomElement(PitchType::get()->pluck('id')),
            'vertical' => Vertical::factory()->create([
                'topic_id' => $topic->getKey(),
            ]),
        ]);

        $response = $this->actingAs($reporter)->postJson(route('betterflow.v1.pitches.create'), [
            ...$pitch->toArray(),
            'form' => [
                'name' => 'Test form',
            ],
        ]);

        $response->assertStatus(Response::HTTP_CREATED);

        $newPitch = Pitch::first();
        $this->assertDatabaseCount('pitches', 1);
        $this->assertEquals(PitchStage::Draft, $newPitch->currentStateId);
    }

    #[Test]
    public function authenticated_users_with_permissions_can_create_a_pitch_with_form(): void
    {
        Pitch::truncate();
        /** @var Reporter $reporter */
        $reporter = Reporter::factory()->verified()->create();
        $topic = Topic::factory()->create();
        $pitch = Pitch::factory()->make([
            'slug' => 'test-123',
            'created_by' => $reporter->getKey(),
            'topic' => $topic->getKey(),
            'pitch_type' => fake()->randomElement(PitchType::get()->pluck('id')),
            'vertical' => Vertical::factory()->create([
                'topic_id' => $topic->getKey(),
            ]),
        ]);

        $pitchForm = [
            'language_code' => 'en-EN',
            'form' => [
                'name' => 'Test Form',
            ],
        ];

        $response = $this->actingAs($reporter)->postJson(route('betterflow.v1.pitches.create'), [
            ...$pitch->toArray(),
            ...$pitchForm,
        ]);

        $response->assertStatus(Response::HTTP_CREATED);

        $newPitch = Pitch::first();

        $this->assertDatabaseCount('pitch_forms', 1);
        $this->assertDatabaseCount('pitches', 1);
        $this->assertDatabaseHas('pitch_forms', [
            'language_code' => 'en-EN',
            'pitch_id' => $newPitch->getKey(),
        ]);
        $this->assertEquals(PitchStage::Draft, $newPitch->currentStateId);
    }

    #[Test]
    public function authenticated_user_can_delete_their_own_pitches(): void
    {
        Pitch::truncate();
        /** @var Reporter $user */
        $user = Reporter::factory()->verified()->create();
        $pitch = Pitch::factory()->create([
            'created_by' => $user->getKey(),
        ]);

        $this->assertDatabaseCount('pitches', 1);
        $response = $this->actingAs($user)
            ->deleteJson(route('betterflow.v1.pitches.delete', $pitch));

        $response->assertStatus(200);
        $this->assertNull(Pitch::find($pitch->getKey()));
    }

    #[Test]
    public function a_pitch_can_be_updated(): void
    {
        Pitch::truncate();
        /** @var Reporter $user */
        $user = Reporter::factory()->verified()->create();
        $pitch = Pitch::factory()->create([
            'slug' => 'test-123',
            'created_by' => $user->getKey(),
        ]);

        $topic = Topic::factory()->create();
        $vertical = Vertical::factory()->create([
            'topic_id' => $topic->getKey(),
        ]);

        $response = $this->actingAs($user)
            ->putJson(route('betterflow.v1.pitches.update.put', $pitch), [
                'topic' => $topic->getKey(),
                'vertical' => $vertical->getKey(),
                'short_name' => 'Test Pitch',
                'slug' => 'test-1234'
            ]);

        $response->assertOk();
    }
}
