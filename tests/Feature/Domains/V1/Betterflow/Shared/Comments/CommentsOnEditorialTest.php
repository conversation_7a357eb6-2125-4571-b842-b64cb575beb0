<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Comments;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Comments\Models\Comment;
use App\Domains\Users\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CommentsOnEditorialTest extends TestCase
{
    private User $user;
    private Editorial $editorial;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->editorial = Editorial::factory()->create();
    }

    #[Test]
    public function can_create_comment_on_editorial(): void
    {
        $this->actingAs($this->user);

        $this->post(route('betterflow.v1.editorials.comments.store', ['editorial' => $this->editorial]), [
            'comment' => ["Test comment"],
        ])->assertCreated();

        $response = $this->getJson(route('betterflow.v1.editorials.show', $this->editorial));


        $response->assertOk();

        $this->assertDatabaseHas('comments', [
            'commentable_type' => 'editorial',
            'commentable_id' => $this->editorial->getKey(),
        ]);
    }

    #[Test]
    public function can_reply_to_comment_on_editorial(): void
    {
        $this->actingAs($this->user);

        $comment = Comment::factory()->create([
            'commentable_type' => 'editorial',
            'commentable_id' => $this->editorial->getKey(),
        ]);

        $this->post(route('betterflow.v1.editorials.comments.store', ['editorial' => $this->editorial]), [
            'comment' => ["Test comment"],
            'comment_id' => $comment->getKey(),
        ])->assertCreated();

        $response = $this->getJson(route('betterflow.v1.editorials.show', $this->editorial));

        $response->assertOk();

        $this->assertDatabaseHas('comments', [
            'commentable_type' => 'editorial',
            'commentable_id' => $this->editorial->getKey(),
        ]);

        $this->assertDatabaseHas('comments', [
            'parent_id' => $comment->getKey(),
            'commentable_type' => 'editorial',
            'commentable_id' => $this->editorial->getKey(),
        ]);
    }
}
