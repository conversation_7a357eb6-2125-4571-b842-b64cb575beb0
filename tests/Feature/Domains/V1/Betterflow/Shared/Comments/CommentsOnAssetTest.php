<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Comments;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Comments\Models\Comment;
use App\Domains\Users\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CommentsOnAssetTest extends TestCase
{
    private User $user;
    private Editorial $editorial;
    private Asset $asset;


    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        $this->editorial = Editorial::factory()->create();
        $this->asset = Asset::factory()->create([
            'editorial_id' => $this->editorial->getKey(),
        ]);
    }

    #[Test]
    public function it_can_comment_on_an_asset(): void 
    {
        $this->actingAs($this->user);

        $this->postJson(route('betterflow.v1.editorials.assets.comments.store', [
            'editorial' => $this->asset->editorial,
            'asset' => $this->asset->slug,
        ]), [
              'comment' => ['Test comment'],  
        ])->assertOk();

        $this->assertDatabaseHas('comments', [
            'commentable_type' => 'asset',
            'commentable_id' => $this->asset->getKey(),
        ]);
    }

    #[Test]
    public function it_can_reply_to_an_asset_comment(): void 
    {
        $this->actingAs($this->user);

        $comment = Comment::factory()->create([
            'commentable_type' => 'asset',
            'commentable_id' => $this->asset->getKey(),
        ]);

        $this->postJson(route('betterflow.v1.editorials.assets.comments.reply', [
            'editorial' => $this->asset->editorial,
            'asset' => $this->asset->slug,
            'comment' => $comment->getKey(),
        ]), [
            'comment' => ['Test comment'],  
        ])->assertOk();

        // $this->assertDatabaseHas('comments', [
        //     'commentable_type' => 'asset',
        //     'commentable_id' => $this->asset->getKey(),
        // ]);

        // $this->assertDatabaseHas('comments', [
        //     'parent_id' => $comment->getKey(),
        // ]);
    }
}
