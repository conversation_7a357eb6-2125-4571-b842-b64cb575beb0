<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Tasks;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class CompleteTaskTest extends TestCase
{
    use FastRefreshDatabase;
    use WithFaker;

    protected function createTask(array $attributes = []): Task
    {
        $pitch = Pitch::factory()->create([
            'public_id' => str()->orderedUuid(),
        ]);

        return Task::factory()->create(array_merge([
            'taskable_type' => get_class($pitch),
            'taskable_id' => $pitch->id,
            'type' => fake()->randomElement(PitchTaskType::cases()),
            'status' => TaskStatus::Pending,
        ], $attributes));
    }

    #[Test]
    public function it_completes_a_task_successfully_with_notes(): void
    {
        // Arrange
        Event::fake([
            TaskCompleted::class,
        ]);
        $task = $this->createTask();
        $notes = 'Task completed successfully';

        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.complete', $task), [
            'notes' => $notes,
        ]);

        // Assert
        $response->assertOk();
        $this->assertEquals(TaskStatus::Completed, $task->fresh()->status);
        $this->assertEquals($notes, $task->fresh()->notes);
        $this->assertNotNull($task->fresh()->completed_at);
        $this->assertEquals(100, $task->fresh()->progress);
        Event::assertDispatched(TaskCompleted::class);
    }

    #[Test]
    public function it_throws_exception_when_completing_already_completed_task_with_notes(): void
    {
        // Arrange
        $task = $this->createTask(['status' => TaskStatus::Completed, 'completed_at' => now()]);

        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.complete', $task), [
            'notes' => 'Notes',
        ]);

        // Assert
        $response->assertStatus(Response::HTTP_CONFLICT);
    }
}
